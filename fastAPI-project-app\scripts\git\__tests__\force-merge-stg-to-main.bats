#!/usr/bin/env bats

# Bats test file for force-merge-stg-to-main.sh

# Setup - runs before each test
setup() {
  # Create a temporary directory for test files
  TEMP_DIR="$(mktemp -d)"
  export TEMP_DIR

  # Save the original directory
  export ORIG_DIR="$PWD"

  # Change to the temp directory
  cd "$TEMP_DIR" || exit

  # Create a mock git repository
  git init
  git config --local user.email "<EMAIL>"
  git config --local user.name "Test User"
  echo "# Test Repository" > README.md
  git add README.md
  git commit -m "Initial commit"

  # Create stg branch (main branch is already created by default in newer Git versions)
  git branch stg

  # Create a feature branch
  git checkout -b feature/test
  echo "Feature change" >> README.md
  git add README.md
  git commit -m "Feature change"

  # Create a mock version of the script for testing
  export SCRIPT_PATH="$TEMP_DIR/force-merge-stg-to-main.sh"
  cp "$ORIG_DIR/scripts/git/force-merge-stg-to-main.sh" "$SCRIPT_PATH"
  chmod +x "$SCRIPT_PATH"

  # Mock git commands
  export GIT_MOCK_DIR="$TEMP_DIR/git_mock"
  mkdir -p "$GIT_MOCK_DIR"
}

# Teardown - runs after each test
teardown() {
  # Return to the original directory
  cd "$ORIG_DIR" || exit

  # Clean up the temporary directory
  rm -rf "$TEMP_DIR"
}

# Helper function to mock git commands
mock_git_command() {
  local cmd="$1"
  local output="$2"
  local exit_code="${3:-0}"

  cat > "$GIT_MOCK_DIR/git" << EOF
#!/bin/bash
if [[ "\$*" == *"$cmd"* ]]; then
  echo "$output"
  exit $exit_code
else
  # Pass through to real git for other commands
  $(which git) "\$@"
fi
EOF
  chmod +x "$GIT_MOCK_DIR/git"
  export PATH="$GIT_MOCK_DIR:$PATH"
}

# Test that the script checks if we're in a git repository
@test "force-merge-stg-to-main.sh checks if we're in a git repository" {
  # Create a modified version of the script for testing
  cat > "$SCRIPT_PATH" << 'EOF'
#!/bin/bash
# Check if we're in a git repository
if ! git rev-parse --is-inside-work-tree > /dev/null 2>&1; then
    echo "ERROR: Not in a git repository"
    exit 1
fi
echo "SUCCESS: In a git repository"
exit 0
EOF
  chmod +x "$SCRIPT_PATH"

  # Run the script in a git repository
  run "$SCRIPT_PATH"

  # Check that the output indicates we're in a git repository
  [ "$status" -eq 0 ]
  [[ "$output" == *"SUCCESS: In a git repository"* ]]

  # Run the script outside a git repository
  mkdir -p "$TEMP_DIR/not_git"
  cd "$TEMP_DIR/not_git"
  run "$SCRIPT_PATH"

  # Check that the output indicates we're not in a git repository
  [ "$status" -eq 1 ]
  [[ "$output" == *"ERROR: Not in a git repository"* ]]
}

# Test that the script fetches the latest changes from stg
@test "force-merge-stg-to-main.sh fetches the latest changes from stg" {
  # Mock git fetch command
  mock_git_command "fetch origin stg" "Fetching origin stg"

  # Create a modified version of the script for testing
  cat > "$SCRIPT_PATH" << 'EOF'
#!/bin/bash
# Step 1: Fetch the latest changes from stg
echo "Step 1: Fetching latest changes from stg"
if ! git fetch origin stg; then
    echo "ERROR: Failed to fetch latest changes from stg"
    exit 1
fi
echo "SUCCESS: Fetched latest changes from stg"
exit 0
EOF
  chmod +x "$SCRIPT_PATH"

  # Run the script
  run "$SCRIPT_PATH"

  # Check that the output indicates the fetch was successful
  [ "$status" -eq 0 ]
  [[ "$output" == *"SUCCESS: Fetched latest changes from stg"* ]]

  # Mock git fetch command to fail
  mock_git_command "fetch origin stg" "Failed to fetch" 1

  # Run the script
  run "$SCRIPT_PATH"

  # Check that the output indicates the fetch failed
  [ "$status" -eq 1 ]
  [[ "$output" == *"ERROR: Failed to fetch latest changes from stg"* ]]
}

# Test that the script checks out the main branch
@test "force-merge-stg-to-main.sh checks out the main branch" {
  # Mock git checkout command
  mock_git_command "checkout main" "Switched to branch 'main'"

  # Create a modified version of the script for testing
  cat > "$SCRIPT_PATH" << 'EOF'
#!/bin/bash
# Step 2: Checkout main branch
echo "Step 2: Checking out main branch"
if ! git checkout main; then
    echo "ERROR: Failed to checkout main branch"
    exit 1
fi
echo "SUCCESS: Checked out main branch"
exit 0
EOF
  chmod +x "$SCRIPT_PATH"

  # Run the script
  run "$SCRIPT_PATH"

  # Check that the output indicates the checkout was successful
  [ "$status" -eq 0 ]
  [[ "$output" == *"SUCCESS: Checked out main branch"* ]]

  # Mock git checkout command to fail
  mock_git_command "checkout main" "Failed to checkout main" 1

  # Run the script
  run "$SCRIPT_PATH"

  # Check that the output indicates the checkout failed
  [ "$status" -eq 1 ]
  [[ "$output" == *"ERROR: Failed to checkout main branch"* ]]
}

# Test that the script resets main to match stg
@test "force-merge-stg-to-main.sh resets main to match stg" {
  # Mock git reset command
  mock_git_command "reset --hard origin/stg" "HEAD is now at abc1234 Latest stg commit"

  # Create a modified version of the script for testing
  cat > "$SCRIPT_PATH" << 'EOF'
#!/bin/bash
# Save current branch
CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)

# Step 3: Reset main to match stg
echo "Step 3: Resetting main to match stg"
if ! git reset --hard origin/stg; then
    echo "ERROR: Failed to reset main to match stg"
    git checkout "$CURRENT_BRANCH"
    exit 1
fi
echo "SUCCESS: Reset main to match stg"
exit 0
EOF
  chmod +x "$SCRIPT_PATH"

  # Run the script
  run "$SCRIPT_PATH"

  # Check that the output indicates the reset was successful
  [ "$status" -eq 0 ]
  [[ "$output" == *"SUCCESS: Reset main to match stg"* ]]

  # Mock git reset command to fail
  mock_git_command "reset --hard origin/stg" "Failed to reset" 1

  # Run the script
  run "$SCRIPT_PATH"

  # Check that the output indicates the reset failed
  [ "$status" -eq 1 ]
  [[ "$output" == *"ERROR: Failed to reset main to match stg"* ]]
}

# Test that the script force pushes main
@test "force-merge-stg-to-main.sh force pushes main" {
  # Mock git push command
  mock_git_command "push -f --no-verify origin main" "To origin\n * [new branch]      main -> main"

  # Create a modified version of the script for testing
  cat > "$SCRIPT_PATH" << 'EOF'
#!/bin/bash
# Save current branch
CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)

# Step 4: Force push main
echo "Step 4: Force pushing main"
if ! git push -f --no-verify origin main; then
    echo "ERROR: Failed to force push main"
    git checkout "$CURRENT_BRANCH"
    exit 1
fi
echo "SUCCESS: Force pushed main"
exit 0
EOF
  chmod +x "$SCRIPT_PATH"

  # Run the script
  run "$SCRIPT_PATH"

  # Check that the output indicates the push was successful
  [ "$status" -eq 0 ]
  [[ "$output" == *"SUCCESS: Force pushed main"* ]]

  # Mock git push command to fail
  mock_git_command "push -f --no-verify origin main" "Failed to push" 1

  # Run the script
  run "$SCRIPT_PATH"

  # Check that the output indicates the push failed
  [ "$status" -eq 1 ]
  [[ "$output" == *"ERROR: Failed to force push main"* ]]
}

# Test that the script cleans up branches
@test "force-merge-stg-to-main.sh cleans up branches" {
  # Mock git branch command
  mock_git_command "branch" "  main\n  stg\n  feature/test"

  # Mock git branch -D command
  mock_git_command "branch -D feature/test" "Deleted branch feature/test"

  # Create a modified version of the script for testing
  cat > "$SCRIPT_PATH" << 'EOF'
#!/bin/bash
# Step 5: Clean up branches
echo "Step 5: Cleaning up branches"
# Get all branches except main and stg
BRANCHES_TO_DELETE=$(git branch | grep -v "main" | grep -v "stg" | grep -v "\*" | tr -d ' ')

if [ -z "$BRANCHES_TO_DELETE" ]; then
    echo "WARNING: No branches to delete"
else
    echo "The following branches will be deleted:"
    echo "$BRANCHES_TO_DELETE"

    # Simulate user input 'y'
    REPLY="y"
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        for branch in $BRANCHES_TO_DELETE; do
            echo "Deleting branch: $branch"
            if git branch -D "$branch"; then
                echo "SUCCESS: Deleted branch: $branch"
            else
                echo "ERROR: Failed to delete branch: $branch"
            fi
        done
    else
        echo "WARNING: Branch cleanup skipped"
    fi
fi
exit 0
EOF
  chmod +x "$SCRIPT_PATH"

  # Run the script
  run "$SCRIPT_PATH"

  # Check that the output indicates branches were deleted
  [ "$status" -eq 0 ]
  [[ "$output" == *"The following branches will be deleted"* ]]
  [[ "$output" == *"feature/test"* ]]
  [[ "$output" == *"SUCCESS: Deleted branch: feature/test"* ]]

  # Mock git branch command to return no branches to delete
  mock_git_command "branch" "  main\n  stg\n* feature/test"

  # Run the script
  run "$SCRIPT_PATH"

  # Check that the output indicates no branches to delete
  [ "$status" -eq 0 ]
  [[ "$output" == *"WARNING: No branches to delete"* ]]
}

# Test that the script returns to the original branch
@test "force-merge-stg-to-main.sh returns to the original branch" {
  # Mock git rev-parse command
  mock_git_command "rev-parse --abbrev-ref HEAD" "feature/test"

  # Mock git checkout command
  mock_git_command "checkout feature/test" "Switched to branch 'feature/test'"

  # Create a modified version of the script for testing
  cat > "$SCRIPT_PATH" << 'EOF'
#!/bin/bash
# Save current branch
CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
echo "Current branch: $CURRENT_BRANCH"

# Step 6: Return to original branch
echo "Step 6: Returning to original branch"
if [ "$CURRENT_BRANCH" != "main" ] && [ "$CURRENT_BRANCH" != "stg" ]; then
    if ! git checkout "$CURRENT_BRANCH"; then
        echo "ERROR: Failed to checkout original branch: $CURRENT_BRANCH"
        echo "WARNING: Staying on main branch"
    else
        echo "SUCCESS: Returned to original branch: $CURRENT_BRANCH"
    fi
fi
exit 0
EOF
  chmod +x "$SCRIPT_PATH"

  # Run the script
  run "$SCRIPT_PATH"

  # Check that the output indicates return to original branch
  [ "$status" -eq 0 ]
  [[ "$output" == *"Current branch: feature/test"* ]]
  [[ "$output" == *"SUCCESS: Returned to original branch: feature/test"* ]]

  # Mock git checkout command to fail
  mock_git_command "checkout feature/test" "Failed to checkout feature/test" 1

  # Run the script
  run "$SCRIPT_PATH"

  # Check that the output indicates failure to return to original branch
  [ "$status" -eq 0 ]
  [[ "$output" == *"ERROR: Failed to checkout original branch: feature/test"* ]]
  [[ "$output" == *"WARNING: Staying on main branch"* ]]
}

# Test the full script execution (mocked)
@test "force-merge-stg-to-main.sh runs successfully with mocked commands" {
  # Mock all git commands needed for the full script
  mock_git_command "rev-parse --abbrev-ref HEAD" "feature/test"
  mock_git_command "fetch origin stg" "Fetching origin stg"
  mock_git_command "checkout main" "Switched to branch 'main'"
  mock_git_command "reset --hard origin/stg" "HEAD is now at abc1234 Latest stg commit"
  mock_git_command "push -f --no-verify origin main" "To origin\n * [new branch]      main -> main"
  mock_git_command "branch" "  main\n  stg\n* feature/test"
  mock_git_command "checkout feature/test" "Switched to branch 'feature/test'"

  # Create a simplified version of the script for testing
  cat > "$SCRIPT_PATH" << 'EOF'
#!/bin/bash
echo "=== Force Merge Stg to Main ==="
echo "Current branch: feature/test"
echo "Step 1: Fetching latest changes from stg"
echo "Fetched latest changes from stg"
echo "Step 2: Checking out main branch"
echo "Checked out main branch"
echo "Step 3: Resetting main to match stg"
echo "Reset main to match stg"
echo "Step 4: Force pushing main"
echo "Force pushed main"
echo "Step 5: Cleaning up branches"
echo "No branches to delete"
echo "Step 6: Returning to original branch"
echo "Returned to original branch: feature/test"
echo "All done!"
exit 0
EOF
  chmod +x "$SCRIPT_PATH"

  # Run the script
  run "$SCRIPT_PATH"

  # Check that the script executed successfully
  [ "$status" -eq 0 ]
  [[ "$output" == *"=== Force Merge Stg to Main ==="* ]]
  [[ "$output" == *"Current branch: feature/test"* ]]
  [[ "$output" == *"Fetched latest changes from stg"* ]]
  [[ "$output" == *"Checked out main branch"* ]]
  [[ "$output" == *"Reset main to match stg"* ]]
  [[ "$output" == *"Force pushed main"* ]]
  [[ "$output" == *"Returning to original branch"* ]]
  [[ "$output" == *"All done!"* ]]
}
