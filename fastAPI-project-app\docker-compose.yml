services:
  # Traefik reverse proxy
  traefik:
    image: traefik:v2.10
    restart: always
    ports:
      - "80:80" # Web
      - "8080:8080" # Dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik.yml:/etc/traefik/traefik.yml:ro
      - ./traefik/dynamic:/etc/traefik/dynamic:ro
    networks:
      - default

  # Frontend with Node.js for development using pnpm
  frontend:
    build:
      context: .
      dockerfile: frontend/Dockerfile.dev
    restart: "no"
    mem_limit: "8G"
    working_dir: /app
    ports:
      - "5173:5173"
    deploy:
      resources:
        limits:
          memory: 8G
    volumes:
      - ./:/app
      - frontend-pnpm-store:/root/.local/share/pnpm/store
      - frontend-node-modules:/app/node_modules
      - frontend-frontend-node-modules:/app/frontend/node_modules
    env_file:
      - .env
    environment:
      - VITE_API_URL=http://api.localhost
      - NODE_OPTIONS=--max-old-space-size=8192
      - PNPM_HOME=/usr/local/bin
      - VITE_BACKEND_CORS_ORIGINS=["http://localhost:3000", "http://localhost:5173", "http://dashboard.localhost"]
    command: sh -c "npm install -g pnpm && pnpm install --force && cd frontend && pnpm dev"
    extra_hosts:
      - "host.docker.internal:host-gateway"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.rule=Host(`dashboard.localhost`)"
      - "traefik.http.services.frontend.loadbalancer.server.port=5173"

  # Frontend test service for running Playwright tests
  frontend-test:
    image: mcr.microsoft.com/playwright:v1.42.1-focal
    depends_on:
      - backend
    volumes:
      - ./:/app
      - ./frontend/playwright-report:/app/frontend/playwright-report
      - ./frontend/test-results:/app/frontend/test-results
    working_dir: /app
    environment:
      - VITE_API_URL=http://backend:8000
      - PLAYWRIGHT_TIMEOUT=60000
      - DEBUG=pw:api
    entrypoint: ["/bin/bash", "-c"]
    command: |
      cd frontend && \
      npm install -g pnpm && \
      pnpm install --no-frozen-lockfile && \
      pnpm add @mui/material @emotion/styled && \
      mkdir -p /app/frontend/playwright/.auth && \
      pnpm exec playwright install --with-deps chromium && \
      # Temporarily move unit tests to prevent them from being discovered
      mkdir -p /tmp/unit-tests && \
      mv /app/frontend/tests/unit/* /tmp/unit-tests/ 2>/dev/null || true && \
      NODE_ENV=test pnpm exec playwright test --project=chromium --timeout=60000 --retries=1 && \
      # Move unit tests back
      mv /tmp/unit-tests/* /app/frontend/tests/unit/ 2>/dev/null || true

  # Backend with improved caching using uv
  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile
    ports:
      - "8000:8000"
    environment:
      - POSTGRES_SERVER=db
      - SMTP_HOST=mailcatcher
      - PYTHONPATH=/app:/app/backend
      - SMTP_PORT=1025
      - SMTP_TLS=false
      - FRONTEND_HOST=http://dashboard.localhost
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
      - ENVIRONMENT=${ENVIRONMENT}
      - PROJECT_NAME=FastAPI Project App
      - SECRET_KEY=${SECRET_KEY}
      - FIRST_SUPERUSER=${FIRST_SUPERUSER}
      - FIRST_SUPERUSER_PASSWORD=${FIRST_SUPERUSER_PASSWORD}
    depends_on:
      db:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "--fail", "http://localhost:8000/api/v1/utils/health-check/", "-H", "Accept: application/json"]
      interval: 30s
      start_period: 40s
      retries: 5
      timeout: 10s
    restart: on-failure
    volumes:
      - .:/app
      - backend-cache:/root/.cache/uv
    command: ["/app/backend/scripts/prestart.sh"]
    extra_hosts:
      - "host.docker.internal:host-gateway"
      - "ghcr.io:*************"
    labels:
      # Traefik labels
      - "traefik.enable=true"
      - "traefik.http.routers.backend.rule=Host(`api.localhost`)"
      - "traefik.http.services.backend.loadbalancer.server.port=8000"
      # Monitoring labels
      - "logging=promtail" # Enable log scraping by Promtail
      - "metrics=prometheus" # Enable metrics scraping by Prometheus
      - "prometheus_port=backend:8000" # Define metrics endpoint address for Prometheus
      - "service_name=backend" # Define service name for logs/metrics

  # Database
  db:
    image: postgres:12
    platform: linux/arm64
    restart: always
    env_file:
      - .env
    environment:
      - PGDATA=/var/lib/postgresql/data/pgdata
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
    healthcheck:
      test: [
        "CMD-SHELL",
        "pg_isready -U ${POSTGRES_USER} -d ${POSTGRES_DB}"
      ]
      interval: 5s
      timeout: 5s
      retries: 5
    volumes:
      - app-db-data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

networks:
  default:
    name: fastapi-project-app_default

volumes:
  frontend-node-modules:
  frontend-frontend-node-modules:
  frontend-pnpm-store:
  backend-cache:
  app-db-data:
