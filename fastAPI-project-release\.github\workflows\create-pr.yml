name: PR Creation - with Machine User Token
# This workflow creates a pull request from a feature or fix branch to the main branch 🍻.

on:
  push:
    branches:
      - 'feat/*'
      - 'feature/*'
      - 'fix/*'
      - 'hotfix/*'

permissions:
  contents: write
  pull-requests: write

jobs:
  create-pr:
    runs-on: ubuntu-latest
    steps:
      - name: Install GitHub CLI
        run: |
          sudo apt-get update
          sudo apt-get install -y gh
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Extract branch name
        shell: bash
        run: echo "BRANCH_NAME=${GITHUB_REF#refs/heads/}" >> $GITHUB_ENV

      - name: Determine branch type
        id: branch-type
        run: |
          if [[ "${{ env.BRANCH_NAME }}" == feat/* ]] || [[ "${{ env.BRANCH_NAME }}" == feature/* ]]; then
            echo "TYPE=feat" >> $GITHUB_ENV
          elif [[ "${{ env.BRANCH_NAME }}" == fix/* ]] || [[ "${{ env.BRANCH_NAME }}" == hotfix/* ]]; then
            echo "TYPE=fix" >> $GITHUB_ENV
          else
            echo "TYPE=other" >> $GITHUB_ENV
          fi

      - name: Create Pull Request with GitHub CLI
        id: create-pr
        env:
          GH_TOKEN: ${{ secrets.MACHINE_USER_TOKEN }}
        run: |
          # Authenticate GitHub CLI

          # Create PR and capture output
          # Check if an open PR already exists from this branch to main
          existing_pr_url=$(gh pr list --head "${{ env.BRANCH_NAME }}" --base main --state open --json url --jq '.[0].url')

          if [ -n "$existing_pr_url" ]; then
            echo "Pull request already exists: $existing_pr_url"
            pr_url="$existing_pr_url"
            pr_number=$(basename "$pr_url" | cut -d'/' -f1)
            # Set outputs
            echo "pull-request-url=$pr_url" >> $GITHUB_OUTPUT
            echo "pull-request-number=$pr_number" >> $GITHUB_OUTPUT
            exit 0
          fi

          # No open PR found, create a new one
          pr_url=$(gh pr create \
            --base main \
            --head "${{ env.BRANCH_NAME }}" \
            --title "${{ env.TYPE }}: Promote ${{ env.BRANCH_NAME }} to main" \
            --body $'This PR promotes branch **${{ env.BRANCH_NAME }}** to **main**.\n\n_Automated PR created by workflow._')

          echo "PR URL: $pr_url"

          # Extract PR number from URL
          pr_number=$(basename "$pr_url" | cut -d'/' -f1)

          # Set outputs
          echo "pull-request-url=$pr_url" >> $GITHUB_OUTPUT
          echo "pull-request-number=$pr_number" >> $GITHUB_OUTPUT

      - name: PR Details
        if: steps.create-pr.outputs.pull-request-number
        run: |
          echo "PR #${{ steps.create-pr.outputs.pull-request-number }} created: ${{ steps.create-pr.outputs.pull-request-url }}"
