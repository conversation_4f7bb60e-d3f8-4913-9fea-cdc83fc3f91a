name: Deploy Documentation

on:
  # Trigger on push to main branch
  push:
    branches:
      - main
  # Also trigger when changes are pushed to core repositories
  workflow_dispatch:

permissions:
  contents: write

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install dependencies
        run: |
          pip install -r requirements.txt
          pip install mkdocs-material

      - name: Create missing documentation directories
        run: |
          # Create directories for documentation files
          mkdir -p docs/app/docs/deployment
          mkdir -p docs/app/docs/workflow
          mkdir -p docs/release/docs
          mkdir -p docs/infra/docs
          mkdir -p docs/app/frontend
          mkdir -p docs/app/backend
          mkdir -p docs/infra/examples/git-workflow
          mkdir -p docs/infra/bootstrap
          mkdir -p docs/infra/rampup

          # Create placeholder files for missing documentation
          touch docs/app/docs/deployment/guide.md
          touch docs/app/docs/workflow/github-actions.md
          touch docs/app/docs/git-hooks.md
          touch docs/app/docs/api-docs.md
          touch docs/app/frontend/README.md
          touch docs/app/backend/README.md
          touch docs/app/BRANCHING_STRATEGY.md
          touch docs/app/ci-cd-workflow.md
          touch docs/app/release-notes.md
          touch docs/release/docs/release-strategy.md
          touch docs/release/docs/troubleshooting.md
          touch docs/release/docs/quick-start.md
          touch docs/release/aws-architecture-diagram.md
          touch docs/release/aws-infrastructure-guide.md
          touch docs/release/container-strategy.md
          touch docs/release/deployment-automation.md
          touch docs/release/infra-setup-guide.md
          touch docs/release/RELEASE_INTEGRATION.md
          touch docs/infra/BRANCHING.md
          touch docs/infra/bootstrap-README.md
          touch docs/infra/examples/git-workflow/example.sh

      - name: Build MkDocs site
        run: mkdocs build

      - name: Configure GitHub Pages
        run: |
          echo "Setting up GitHub Pages..."
          # Create a .nojekyll file to bypass Jekyll processing
          touch site/.nojekyll
          # Create a simple test file to verify GitHub Pages is working
          echo "<html><body><h1>GitHub Pages Test</h1><p>This is a test page.</p></body></html>" > site/test.html

      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./site
          force_orphan: true
          cname: # Leave empty, no custom domain
          enable_jekyll: false
          user_name: 'github-actions[bot]'
          user_email: 'github-actions[bot]@users.noreply.github.com'
          commit_message: 'Deploy documentation to GitHub Pages'
          full_commit_message: 'Deploy documentation to GitHub Pages [skip ci]'

      - name: Create GitHub Release
        if: github.event_name == 'push' && github.ref == 'refs/heads/main'
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          # Install GitHub CLI
          sudo apt-get update
          sudo apt-get install -y gh

          # Get the current date for the release tag
          RELEASE_DATE=$(date +"%Y.%m.%d")

          # Create a release tag
          git tag -a "v$RELEASE_DATE" -m "Documentation release $RELEASE_DATE" || echo "Tag already exists"
          git push origin "v$RELEASE_DATE" || echo "Tag already pushed"

          # Create a GitHub release
          gh release create "v$RELEASE_DATE" \
            --title "Documentation Release $RELEASE_DATE" \
            --notes "Documentation update released on $RELEASE_DATE" \
            --target main || echo "Release already exists"
