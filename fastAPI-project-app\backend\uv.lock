# This file was autogenerated by uv via the following command:
#    uv pip compile pyproject.toml -o uv.lock --python-version 3.11
alembic==1.15.2
    # via app (pyproject.toml)
annotated-types==0.7.0
    # via pydantic
anyio==4.9.0
    # via
    #   httpx
    #   starlette
    #   watchfiles
bcrypt==4.0.1
    # via
    #   app (pyproject.toml)
    #   passlib
cachetools==5.5.2
    # via premailer
certifi==2025.1.31
    # via
    #   httpcore
    #   httpx
    #   requests
    #   sentry-sdk
chardet==5.2.0
    # via emails
charset-normalizer==3.4.1
    # via requests
click==8.1.8
    # via
    #   rich-toolkit
    #   typer
    #   uvicorn
cssselect==1.3.0
    # via premailer
cssutils==2.11.1
    # via
    #   emails
    #   premailer
dnspython==2.7.0
    # via email-validator
email-validator==2.2.0
    # via
    #   app (pyproject.toml)
    #   fastapi
emails==0.6
    # via app (pyproject.toml)
fastapi==0.115.12
    # via
    #   app (pyproject.toml)
    #   sentry-sdk
fastapi-cli==0.0.7
    # via fastapi
h11==0.14.0
    # via
    #   httpcore
    #   uvicorn
httpcore==1.0.7
    # via httpx
httptools==0.6.4
    # via uvicorn
httpx==0.28.1
    # via
    #   app (pyproject.toml)
    #   fastapi
idna==3.10
    # via
    #   anyio
    #   email-validator
    #   httpx
    #   requests
jinja2==3.1.6
    # via
    #   app (pyproject.toml)
    #   fastapi
lxml==5.3.2
    # via
    #   emails
    #   premailer
mako==1.3.9
    # via alembic
markdown-it-py==3.0.0
    # via rich
markupsafe==3.0.2
    # via
    #   jinja2
    #   mako
mdurl==0.1.2
    # via markdown-it-py
more-itertools==10.6.0
    # via cssutils
passlib==1.7.4
    # via app (pyproject.toml)
premailer==3.10.0
    # via emails
psycopg==3.2.6
    # via app (pyproject.toml)
psycopg-binary==3.2.6
    # via psycopg
pydantic==2.11.3
    # via
    #   app (pyproject.toml)
    #   fastapi
    #   pydantic-settings
    #   sqlmodel
pydantic-core==2.33.1
    # via pydantic
pydantic-settings==2.8.1
    # via app (pyproject.toml)
pygments==2.19.1
    # via rich
pyjwt==2.10.1
    # via app (pyproject.toml)
python-dateutil==2.9.0.post0
    # via emails
python-dotenv==1.1.0
    # via
    #   pydantic-settings
    #   uvicorn
python-multipart==0.0.20
    # via
    #   app (pyproject.toml)
    #   fastapi
pyyaml==6.0.2
    # via uvicorn
requests==2.32.3
    # via
    #   emails
    #   premailer
rich==14.0.0
    # via
    #   rich-toolkit
    #   typer
rich-toolkit==0.14.1
    # via fastapi-cli
sentry-sdk==2.25.1
    # via app (pyproject.toml)
shellingham==1.5.4
    # via typer
six==1.17.0
    # via python-dateutil
sniffio==1.3.1
    # via anyio
sqlalchemy==2.0.40
    # via
    #   alembic
    #   sqlmodel
sqlmodel==0.0.24
    # via app (pyproject.toml)
starlette==0.46.1
    # via fastapi
tenacity==8.5.0
    # via app (pyproject.toml)
typer==0.15.2
    # via fastapi-cli
typing-extensions==4.13.1
    # via
    #   alembic
    #   anyio
    #   fastapi
    #   psycopg
    #   pydantic
    #   pydantic-core
    #   rich-toolkit
    #   sqlalchemy
    #   typer
    #   typing-inspection
typing-inspection==0.4.0
    # via pydantic
urllib3==2.3.0
    # via
    #   requests
    #   sentry-sdk
uvicorn==0.29.0
    # via
    #   app (pyproject.toml)
    #   fastapi
    #   fastapi-cli
uvloop==0.21.0
    # via uvicorn
watchfiles==1.0.5
    # via uvicorn
websockets==15.0.1
    # via uvicorn
