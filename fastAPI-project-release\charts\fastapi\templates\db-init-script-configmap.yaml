apiVersion: v1
kind: ConfigMap
metadata:
  name: db-init-script
  namespace: {{ .Values.app.namespace }}
data:
  init-db.py: |
    import socket
    import time
    import os
    import sys
    
    print("Waiting for PostgreSQL to be ready...")
    postgres_host = os.environ.get("POSTGRES_SERVER", "postgres")
    postgres_port = int(os.environ.get("POSTGRES_PORT", "5432"))
    
    # Try to connect to PostgreSQL
    for i in range(30):
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            s.settimeout(1)
            s.connect((postgres_host, postgres_port))
            s.close()
            print("PostgreSQL is up!")
            break
        except (socket.error, socket.timeout):
            print(f"PostgreSQL is unavailable - sleeping for 5 seconds (attempt {i+1}/30)")
            time.sleep(5)
            if i == 29:
                print("Failed to connect to PostgreSQL after 30 attempts")
                sys.exit(1)

    # Run migrations
    print("Running database migrations...")
    import alembic.config
    import os
    os.chdir("/app")
    alembic.config.main(argv=["upgrade", "head"])