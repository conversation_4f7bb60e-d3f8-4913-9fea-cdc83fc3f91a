{"name": "scripts", "private": true, "type": "module", "scripts": {"test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:ts": "vitest run **/*.test.ts", "test:py": "python -m unittest discover -s . -p 'test_*.py'", "test:sh": "find . -name 'test_*.sh' -exec bash {} ';'", "lint": "biome check --apply-unsafe .", "type-check": "tsc --noEmit"}, "devDependencies": {"ts-node": "^10.9.2", "typescript": "^5.8.3", "vitest": "^1.6.1"}}