# Base Environment Variables
# This file contains common settings for all environments
# Copy this file to .env.base and fill in the values

# AWS Credentials - Required for all AWS operations
# You can find these in your AWS Console under IAM -> Users -> Security credentials
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key

# AWS Account Information - Required for resource naming and permissions
# Find your AWS Account ID in the AWS Console (top-right dropdown)
AWS_ACCOUNT_ID=your-aws-account-id

# Project Configuration
# These values are used for resource naming and tagging
PROJECT_NAME=fastapi-project

# Resource Naming (Optional - will be generated if not provided)
# Uncomment and customize if you want specific resource names
# Resource Naming (from .env in root directory)
# TERRAFORM_STATE_BUCKET_PREFIX=terraform-state
# TERRAFORM_LOGS_BUCKET_PREFIX=terraform-logs
# DYNAMODB_TABLE_PREFIX=terraform-lock