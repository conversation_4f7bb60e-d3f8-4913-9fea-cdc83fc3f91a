name: Update Helm Image Tag

on:
  workflow_dispatch:
    inputs:
      tag:
        description: 'Docker image tag to deploy'
        required: true
        type: string
      env:
        description: 'Deployment environment (dev, staging, production)'
        required: true
        default: production
        type: string
  release:
    types: [published, prereleased]

jobs:
  update-helm:
    runs-on: ubuntu-latest
    env:
      GIT_AUTHOR_NAME: github-actions
      GIT_AUTHOR_EMAIL: <EMAIL>
      GIT_COMMITTER_NAME: github-actions
      GIT_COMMITTER_EMAIL: <EMAIL>
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 0  # Fetch all history for proper branch detection

      - name: Install yq
        run: |
          sudo wget -qO /usr/local/bin/yq https://github.com/mikefarah/yq/releases/download/v4.44.1/yq_linux_amd64
          sudo chmod +x /usr/local/bin/yq

      - name: Debug Event Information
        run: |
          echo "Event name: ${{ github.event_name }}"
          echo "Event action: ${{ github.event.action }}"
          if [ "${{ github.event_name }}" = "release" ]; then
            echo "Release tag: ${{ github.event.release.tag_name }}"
            echo "Release name: ${{ github.event.release.name }}"
            echo "Release draft: ${{ github.event.release.draft }}"
            echo "Release prerelease: ${{ github.event.release.prerelease }}"
          else
            echo "Workflow dispatch inputs:"
            echo "  Tag: ${{ github.event.inputs.tag }}"
            echo "  Environment: ${{ github.event.inputs.env }}"
          fi

      - name: Determine tag and environment
        id: vars
        run: |
          # Determine tag
          if [ "${{ github.event_name }}" = "release" ]; then
            echo "tag=${{ github.event.release.tag_name }}" >> $GITHUB_OUTPUT
            # For releases, determine environment based on tag format
            if [[ "${{ github.event.release.tag_name }}" == v* ]]; then
              echo "env=production" >> $GITHUB_OUTPUT
            else
              echo "env=staging" >> $GITHUB_OUTPUT
            fi
            echo "Triggered by release event for tag ${{ github.event.release.tag_name }}"
          else
            TAG="${{ github.event.inputs.tag }}"
            ENV="${{ github.event.inputs.env }}"

            # Handle the new tag format for staging (vstg-[GITHASH])
            if [[ "$TAG" == vstg-* ]]; then
              # This is a staging tag with semantic version
              ENV="staging"
              echo "Detected vstg- prefix, setting environment to staging"
            elif [[ "$TAG" == stg-* ]]; then
              # This is a regular staging tag
              ENV="staging"
              echo "Detected stg- prefix, setting environment to staging"
            elif [[ "$TAG" == v* ]]; then
              # This is a production tag
              ENV="production"
              echo "Detected v prefix, setting environment to production"
            fi

            echo "tag=$TAG" >> $GITHUB_OUTPUT
            echo "env=$ENV" >> $GITHUB_OUTPUT
            echo "Triggered by workflow_dispatch with tag=$TAG, env=$ENV"
          fi

          # Print determined values for debugging
          echo "Using tag: $(cat $GITHUB_OUTPUT | grep tag | cut -d'=' -f2)"
          echo "Using environment: $(cat $GITHUB_OUTPUT | grep env | cut -d'=' -f2)"

      - name: Update Helm values with new image tag
        id: update_helm
        run: |
          echo "::group::Environment and Tag Information"
          echo "Tag: ${{ steps.vars.outputs.tag }}"
          echo "Environment: ${{ steps.vars.outputs.env }}"
          echo "::endgroup::"

          echo "::group::Helm Values File Validation"
          VALUES_FILE="config/helm/${{ steps.vars.outputs.env }}.yaml"
          if [ ! -f "$VALUES_FILE" ]; then
            echo "::error::Values file $VALUES_FILE does not exist"
            echo "Available files in config/helm/:"
            ls -la config/helm/
            exit 1
          fi
          echo "✅ Values file exists: $VALUES_FILE"
          echo "::endgroup::"

          echo "::group::Current Tag Values"
          CURRENT_BACKEND_TAG=$(yq e '.backend.tag' "$VALUES_FILE" || echo "ERROR")
          CURRENT_FRONTEND_TAG=$(yq e '.frontend.tag' "$VALUES_FILE" || echo "ERROR")
          
          if [[ "$CURRENT_BACKEND_TAG" == "ERROR" || "$CURRENT_FRONTEND_TAG" == "ERROR" ]]; then
            echo "::error::Failed to read current tag values"
            exit 1
          fi
          
          echo "Current backend tag: $CURRENT_BACKEND_TAG"
          echo "Current frontend tag: $CURRENT_FRONTEND_TAG"
          echo "::endgroup::"

          echo "::group::Updating Image Tags"
          TAG="${{ steps.vars.outputs.tag }}"
          ENV="${{ steps.vars.outputs.env }}"

          if ! make update-image TAG="$TAG" ENV="$ENV"; then
            echo "::error::Failed to update image tags"
            exit 1
          fi
          echo "::endgroup::"

          echo "::group::Verification"
          NEW_BACKEND_TAG=$(yq e '.backend.tag' "$VALUES_FILE")
          NEW_FRONTEND_TAG=$(yq e '.frontend.tag' "$VALUES_FILE")

          if [[ "$NEW_BACKEND_TAG" != "$TAG" || "$NEW_FRONTEND_TAG" != "$TAG" ]]; then
            echo "::error::Tag verification failed!"
            echo "Expected: $TAG"
            echo "Got: backend=$NEW_BACKEND_TAG, frontend=$NEW_FRONTEND_TAG"
            exit 1
          fi

          echo "✅ Tags successfully updated and verified"
          echo "Backend: $NEW_BACKEND_TAG"
          echo "Frontend: $NEW_FRONTEND_TAG"
          echo "::endgroup::"

          # Set outputs for use in later steps
          echo "updated=true" >> $GITHUB_OUTPUT
          echo "old_backend_tag=$CURRENT_BACKEND_TAG" >> $GITHUB_OUTPUT
          echo "old_frontend_tag=$CURRENT_FRONTEND_TAG" >> $GITHUB_OUTPUT

      - name: Commit changes
        id: commit
        if: steps.update_helm.outputs.updated == 'true'
        run: |
          echo "::group::Git Configuration"
          git config --global user.name "github-actions"
          git config --global user.email "<EMAIL>"
          echo "::endgroup::"

          DEFAULT_BRANCH="main"
          echo "Default branch: $DEFAULT_BRANCH"

          echo "::group::Branch Creation"
          TIMESTAMP=$(date +%Y%m%d%H%M%S)
          TAG_SAFE=$(echo "${{ steps.vars.outputs.tag }}" | sed 's/[^a-zA-Z0-9]/-/g')
          BRANCH_NAME="update-image-${TAG_SAFE}-${TIMESTAMP}"
          echo "Creating new branch: $BRANCH_NAME"
          git checkout -b "$BRANCH_NAME"
          echo "::endgroup::"

          echo "::group::Git Status and Changes"
          git status
          git add config/helm/*.yaml
          
          COMMIT_MSG=$(cat << 'EOF'
          chore: update image tags for ${{ steps.vars.outputs.env }} environment

          From:
          - backend: ${{ steps.update_helm.outputs.old_backend_tag }}
          - frontend: ${{ steps.update_helm.outputs.old_frontend_tag }}

          To:
          - backend: ${{ steps.vars.outputs.tag }}
          - frontend: ${{ steps.vars.outputs.tag }}

          Environment: ${{ steps.vars.outputs.env }}
          EOF
          )

          if git commit -m "$COMMIT_MSG"; then
            echo "✅ Changes committed successfully"
            if git push origin "$BRANCH_NAME"; then
              echo "✅ Changes pushed successfully"
              echo "branch_name=$BRANCH_NAME" >> $GITHUB_OUTPUT
              echo "default_branch=$DEFAULT_BRANCH" >> $GITHUB_OUTPUT
              echo "has_changes=true" >> $GITHUB_OUTPUT
            else
              echo "::error::Failed to push changes to remote"
              exit 1
            fi
          else
            echo "No changes to commit. Possible reasons:"
            echo "1. Tags were already up to date"
            echo "2. No modifications were made to the files"
            git status
            echo "has_changes=false" >> $GITHUB_OUTPUT
          fi
          echo "::endgroup::"

      - name: Create Pull Request
        if: steps.commit.outputs.has_changes == 'true'
        id: create_pr
        uses: peter-evans/create-pull-request@v6
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          commit-message: "chore: update image tags for ${{ steps.vars.outputs.env }} environment"
          branch: ${{ steps.commit.outputs.branch_name }}
          base: ${{ steps.commit.outputs.default_branch }}
          title: "Update image tags to ${{ steps.vars.outputs.tag }} for ${{ steps.vars.outputs.env }}"
          body: |
            ## Changes Summary
            This PR updates the image tags in the Helm values files for the **${{ steps.vars.outputs.env }}** environment.

            ### Tag Updates
            **From:**
            - Backend: `${{ steps.update_helm.outputs.old_backend_tag }}`
            - Frontend: `${{ steps.update_helm.outputs.old_frontend_tag }}`

            **To:**
            - Backend: `${{ steps.vars.outputs.tag }}`
            - Frontend: `${{ steps.vars.outputs.tag }}`

            ### Additional Information
            - Environment: `${{ steps.vars.outputs.env }}`
            - Triggered by: `${{ github.event_name }}`
            - Workflow run: [View Run](https://github.com/${{ github.repository }}/actions/runs/${{ github.run_id }})

            This PR was automatically created by the update-helm workflow.
          labels: |
            automated-pr
            helm-update
            env/${{ steps.vars.outputs.env }}
          delete-branch: true

      - name: Auto-merge Pull Request
        if: steps.create_pr.outputs.pull-request-number
        run: |
          PR_NUMBER=${{ steps.create_pr.outputs.pull-request-number }}
          echo "Created PR #${PR_NUMBER}"

          # Install GitHub CLI
          curl -fsSL https://cli.github.com/packages/githubcli-archive-keyring.gpg | sudo dd of=/usr/share/keyrings/githubcli-archive-keyring.gpg
          echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" | sudo tee /etc/apt/sources.list.d/github-cli.list > /dev/null
          sudo apt update
          sudo apt install gh

          # Authenticate with GitHub CLI
          echo "${{ secrets.MACHINE_USER_TOKEN }}" | gh auth login --with-token

          # Enable auto-merge for the PR
          echo "Enabling auto-merge for PR #${PR_NUMBER}"
          gh pr merge ${PR_NUMBER} --auto --merge

          echo "Auto-merge enabled for PR #${PR_NUMBER}"

      - name: Create GitHub Release (only if production and not triggered by release)
        if: ${{ startsWith(steps.vars.outputs.tag, 'v') && github.event_name != 'release' && (github.event.inputs.env == 'production') }}
        uses: softprops/action-gh-release@v2.0.2
        with:
          tag_name: ${{ steps.vars.outputs.tag }}
          name: Release ${{ steps.vars.outputs.tag }}
          generate_release_notes: true