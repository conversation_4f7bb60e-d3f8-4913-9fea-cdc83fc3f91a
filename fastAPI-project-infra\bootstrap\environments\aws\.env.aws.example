# AWS Environment Variables
# This file contains settings specific to the AWS environment

# ===== ROLE-BASED AUTHENTICATION (RECOMMENDED) =====
# To use role-based authentication, set your AWS account ID and ensure the role exists
# You need minimal permissions to assume the role (sts:AssumeRole)
AWS_ACCOUNT_ID=YOUR_AWS_ACCOUNT_ID
AWS_BOOTSTRAP_ROLE_NAME=AWS_BOOTSTRAP_ROLE_NAME
AWS_BOOTSTRAP_POLICY_NAME=AWS_BOOTSTRAP_POLICY_NAME

# ===== USER-BASED AUTHENTICATION (LEGACY) =====
# These credentials are only needed if you're not using role-based authentication
# If using roles, you can provide credentials with minimal permissions to assume the role
# or rely on your AWS CLI configuration (~/.aws/credentials)
# AWS_ACCESS_KEY_ID=YOUR_ACCESS_KEY
# AWS_SECRET_ACCESS_KEY=YOUR_SECRET_KEY

# AWS Region Configuration
AWS_DEFAULT_REGION=us-east-1

# Environment setting
ENVIRONMENT=aws
PROJECT_NAME=fastapi-project

# S3 bucket configuration
S3_FORCE_PATH_STYLE=false

# Terraform variables
TF_VAR_aws_region=${AWS_DEFAULT_REGION}
TF_VAR_account_id=${AWS_ACCOUNT_ID}