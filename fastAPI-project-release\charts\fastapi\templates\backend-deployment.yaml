apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ .Values.backend.name }}
  namespace: {{ .Values.app.namespace }}
  labels:
    app: {{ .Values.backend.name }}
spec:
  replicas: {{ .Values.backend.replicas }}
  selector:
    matchLabels:
      app: {{ .Values.backend.name }}
  template:
    metadata:
      labels:
        app: {{ .Values.backend.name }}
    spec:
      {{- if .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml .Values.imagePullSecrets | nindent 8 }}
      {{- end }}
      containers:
        - name: {{ .Values.backend.name }}
          image: "{{ .Values.backend.image }}:{{ .Values.backend.tag }}"
          imagePullPolicy: IfNotPresent
          ports:
            - name: http
              containerPort: {{ .Values.backend.port }}
              protocol: TCP
          env:
            - name: DATABASE_URL
              valueFrom:
                configMapKeyRef:
                  name: {{ .Values.app.name }}-config
                  key: databaseUrl
            - name: ALLOWED_ORIGINS
              valueFrom:
                configMapKeyRef:
                  name: {{ .Values.app.name }}-config
                  key: allowedOrigins
            - name: CORS_SETTINGS
              valueFrom:
                configMapKeyRef:
                  name: {{ .Values.app.name }}-config
                  key: corsSettings
            - name: DEBUG_MODE
              valueFrom:
                configMapKeyRef:
                  name: {{ .Values.app.name }}-config
                  key: debugMode
            - name: SECRET_KEY
              valueFrom:
                configMapKeyRef:
                  name: {{ .Values.app.name }}-config
                  key: secretKey
          {{- with .Values.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.livenessProbe }}
          livenessProbe:
            httpGet:
              path: /health
              port: http
            {{- toYaml . | nindent 12 }}
          {{- end }}
          {{- with .Values.readinessProbe }}
          readinessProbe:
            httpGet:
              path: /health
              port: http
            {{- toYaml . | nindent 12 }}
          {{- end }}
      {{- with .Values.securityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.podSecurityContext }}
      podSecurityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
---
{{- if .Values.autoscaling.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ .Values.backend.name }}-hpa
  namespace: {{ .Values.app.namespace }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ .Values.backend.name }}
  minReplicas: {{ .Values.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.autoscaling.maxReplicas }}
  metrics:
    - type: Resource
      resource:
        name: cpu
        target:
          type: Utilization
          averageUtilization: {{ .Values.autoscaling.targetCPUUtilizationPercentage }}
{{- end }}