site_name: FastAPI Project Documentation
site_description: Documentation for the FastAPI Project
site_author: datascientest-fastAPI-project-group-25
site_url: https://datascientest-fastapi-project-group-25.github.io/fastAPI-project-docs/
repo_url: https://github.com/datascientest-fastAPI-project-group-25/fastAPI-project-docs
use_directory_urls: false

theme:
  name: material
  palette:
    primary: indigo
    accent: indigo
  features:
    - navigation.tabs
    - navigation.sections
    - navigation.expand
    - toc.integrate
    - search.suggest
    - search.highlight
    - content.code.copy

nav:
  - Home: index.md
  - Contributing: contributing.md
  - App:
    - Overview: app/README.md
    - Documentation:
      - API Documentation: app/docs/api-docs.md
      - Deployment Guide: app/docs/deployment/guide.md
      - Git Hooks: app/docs/git-hooks.md
      - GitHub Actions: app/docs/workflow/github-actions.md
    - Frontend:
      - Overview: app/frontend/README.md
    - Backend:
      - Overview: app/backend/README.md
    - Branching Strategy: app/BRANCHING_STRATEGY.md
    - CI/CD Workflow: app/ci-cd-workflow.md
    - Release Notes: app/release-notes.md
    - Monitoring:
      - Overview: app/monitoring/README.md
    - Logging:
      - Overview: app/logging/README.md
  - Release:
    - Overview: release/README.md
    - Documentation:
      - Release Strategy: release/docs/release-strategy.md
      - Troubleshooting: release/docs/troubleshooting.md
      - Quick Start: release/docs/quick-start.md
      - Dockerfile Specification: release/dockerfile-spec.md
    - AWS Architecture: release/aws-architecture-diagram.md
    - AWS Infrastructure Guide: release/aws-infrastructure-guide.md
    - Container Strategy: release/container-strategy.md
    - Deployment Automation: release/deployment-automation.md
    - Infrastructure Setup: release/infra-setup-guide.md
    - Release Integration: release/RELEASE_INTEGRATION.md
    - Monitoring:
      - Overview: release/monitoring/README.md
    - Logging:
      - Overview: release/logging/README.md
  - Infrastructure:
    - Overview: infra/README.md
    - Branching Strategy: infra/BRANCHING.md
    - Bootstrap Guide: infra/bootstrap-README.md
    - Examples:
      - Git Workflow: infra/examples/git-workflow/example.sh
    - Monitoring:
      - Overview: infra/monitoring/README.md
    - Logging:
      - Overview: infra/logging/README.md

markdown_extensions:
  - admonition
  - codehilite
  - pymdownx.highlight
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:pymdownx.superfences.fence_code_format
  - pymdownx.tabbed
  - pymdownx.tasklist
  - toc:
      permalink: true

plugins:
  - search
  - mermaid2
