# Test Environment Variables
# This file contains settings specific to the test environment
# Copy this file to .env.test and fill in the values
#
# NOTE: Common settings like AWS credentials, account ID, and project name
# should be defined in .env.base instead of here.
# See .env.base.example for more information.

# AWS Region Configuration
AWS_DEFAULT_REGION=eu-west-2

# Environment setting
ENVIRONMENT=test

# OpenID Connect Configuration for GitHub Actions
OPEN_ID_CONNECT_ROLE=FastAPIProjectInfraRole
OPEN_ID_CONNECT_PROVIDER=token.actions.githubusercontent.com

# Mock Mode Configuration
# Set to true to use mock AWS operations (recommended for CI/CD)
USE_MOCK_AWS=true

# Test-specific Configuration
# These values are used only for testing
TEST_S3_BUCKET_PREFIX=test-bucket
TEST_DYNAMODB_TABLE_PREFIX=test-table

# GitHub Actions Configuration
# These are used by the GitHub Actions workflow
GITHUB_TOKEN=your-github-token

# Terraform Configuration
# These control Terraform behavior during tests
TF_CLI_ARGS_plan="-compact-warnings"
TF_CLI_ARGS_apply="-compact-warnings -auto-approve"
TF_CLI_ARGS_destroy="-auto-approve"
