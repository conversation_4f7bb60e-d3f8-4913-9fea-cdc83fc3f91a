name: Reusable Image Build and Push

on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
        description: "Environment to build for (stg or prod)"
      version_bump_type:
        required: false
        type: string
        default: "patch"
        description: "Type of version bump (major, minor, patch)"
      image_tag_prefix:
        required: false
        type: string
        default: ""
        description: "Optional prefix for image tags"

permissions:
  contents: write
  packages: write

jobs:
  bump-version:
    runs-on: ubuntu-latest
    outputs:
      new_version: ${{ steps.bump-version.outputs.new_version }}
      bump_type: ${{ inputs.version_bump_type }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Bump version
        id: bump-version
        run: |
          set -e  # Exit immediately if a command exits with a non-zero status
          BUMP_TYPE="${{ inputs.version_bump_type }}"
          echo "Attempting to bump version with type: $BUMP_TYPE"

          # Check if version.py exists and is executable
          if [ ! -f "version.py" ]; then
            echo "::error::version.py file not found"
            exit 1
          fi

          # Try to bump the version
          if ! python version.py bump $BUMP_TYPE; then
            echo "::error::Failed to bump version"
            exit 1
          fi

          # Get the new version
          NEW_VERSION=$(python version.py get)
          if [ -z "$NEW_VERSION" ]; then
            echo "::error::Failed to get new version"
            exit 1
          fi

          echo "new_version=$NEW_VERSION" >> $GITHUB_OUTPUT
          echo "Version successfully bumped to $NEW_VERSION"

      - name: Commit and push version bump
        id: commit-version
        continue-on-error: true
        run: |
          set -e  # Exit immediately if a command exits with a non-zero status

          echo "Configuring git user"
          git config --local user.email "<EMAIL>"
          git config --local user.name "GitHub Action"

          echo "Checking for changes to VERSION file"
          if ! git diff --quiet VERSION; then
            echo "Changes detected in VERSION file, committing"
            git add VERSION
            git commit -m "chore: bump version to ${{ steps.bump-version.outputs.new_version }} [skip ci]"

            echo "Pushing changes"
            if ! git push; then
              echo "Warning: Failed to push version bump commit, but continuing workflow"
            else
              echo "Successfully committed and pushed version bump"
            fi
          else
            echo "No changes to VERSION file, skipping commit"
          fi

  build-and-push:
    needs: bump-version
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.ref }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Set image tags
        id: set-tags
        run: |
          ENV="${{ inputs.environment }}"
          VERSION="${{ needs.bump-version.outputs.new_version }}"
          PREFIX="${{ inputs.image_tag_prefix }}"
          SHORT_SHA="$(echo ${{ github.sha }} | cut -c1-7)"

          if [ -n "$PREFIX" ]; then
            PREFIX="${PREFIX}-"
          fi

          if [ "$ENV" == "stg" ]; then
            REPO_OWNER=$(echo "${{ github.repository_owner }}" | tr '[:upper:]' '[:lower:]')
            REPO_NAME=$(echo "${{ github.event.repository.name }}" | tr '[:upper:]' '[:lower:]')
            # Staging: Both use stg-SHA
            BACKEND_TAGS="ghcr.io/${REPO_OWNER}/${REPO_NAME}-backend:${PREFIX}stg-${SHORT_SHA}"
            FRONTEND_TAGS="ghcr.io/${REPO_OWNER}/${REPO_NAME}-frontend:${PREFIX}stg-${SHORT_SHA}"
          else # Assume prod or other environments
            REPO_OWNER=$(echo "${{ github.repository_owner }}" | tr '[:upper:]' '[:lower:]')
            REPO_NAME=$(echo "${{ github.event.repository.name }}" | tr '[:upper:]' '[:lower:]')
            # Production: Use version and latest
            BACKEND_TAGS="ghcr.io/${REPO_OWNER}/${REPO_NAME}-backend:${PREFIX}${VERSION},ghcr.io/${REPO_OWNER}/${REPO_NAME}-backend:latest"
            FRONTEND_TAGS="ghcr.io/${REPO_OWNER}/${REPO_NAME}-frontend:${PREFIX}${VERSION},ghcr.io/${REPO_OWNER}/${REPO_NAME}-frontend:latest"
          fi

          echo "backend_tags=$BACKEND_TAGS" >> $GITHUB_OUTPUT
          echo "frontend_tags=$FRONTEND_TAGS" >> $GITHUB_OUTPUT

      - name: Build and push backend image
        id: build-backend
        uses: docker/build-push-action@v6
        with:
          context: ./backend
          file: ./backend/Dockerfile
          push: true
          tags: ${{ steps.set-tags.outputs.backend_tags }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            APP_VERSION=${{ needs.bump-version.outputs.new_version }}
            GIT_HASH=${{ github.sha }}
            BRANCH_TYPE=${{ inputs.environment }}
          # Enable provenance attestation for supply chain security
          provenance: true
          # Add labels for better image management
          labels: |
            org.opencontainers.image.title=backend
            org.opencontainers.image.version=${{ needs.bump-version.outputs.new_version }}
            org.opencontainers.image.revision=${{ github.sha }}
            org.opencontainers.image.created=${{ github.event.repository.updated_at }}
            org.opencontainers.image.source=${{ github.server_url }}/${{ github.repository }}
            org.opencontainers.image.environment=${{ inputs.environment }}

      - name: Verify backend image build
        if: steps.build-backend.outcome != 'success'
        run: |
          echo "::error::Backend image build failed"
          exit 1

      - name: Build and push frontend image
        id: build-frontend
        uses: docker/build-push-action@v6
        with:
          context: ./frontend
          file: ./frontend/Dockerfile
          push: true
          tags: ${{ steps.set-tags.outputs.frontend_tags }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          build-args: |
            APP_VERSION=${{ needs.bump-version.outputs.new_version }}
            GIT_HASH=${{ github.sha }}
            BRANCH_TYPE=${{ inputs.environment }}
          # Enable provenance attestation for supply chain security
          provenance: true
          # Add labels for better image management
          labels: |
            org.opencontainers.image.title=frontend
            org.opencontainers.image.version=${{ needs.bump-version.outputs.new_version }}
            org.opencontainers.image.revision=${{ github.sha }}
            org.opencontainers.image.created=${{ github.event.repository.updated_at }}
            org.opencontainers.image.source=${{ github.server_url }}/${{ github.repository }}
            org.opencontainers.image.environment=${{ inputs.environment }}

      - name: Verify frontend image build
        if: steps.build-frontend.outcome != 'success'
        run: |
          echo "::error::Frontend image build failed"
          exit 1

      - name: Extract backend image tag
        id: extract-backend-tag
        run: |
          BACKEND_TAG="${{ steps.set-tags.outputs.backend_tags }}"
          echo "image_tag=$BACKEND_TAG" >> $GITHUB_OUTPUT

      - name: Run Trivy vulnerability scanner for backend
        uses: aquasecurity/trivy-action@master
        if: steps.extract-backend-tag.outcome == 'success'
        continue-on-error: true
        with:
          image-ref: ${{ steps.extract-backend-tag.outputs.image_tag }}
          format: 'sarif'
          output: 'trivy-backend-results.sarif'
          severity: 'CRITICAL,HIGH'
          exit-code: '0'  # Don't fail the build, just report
          ignore-unfixed: true

      - name: Extract frontend image tag
        id: extract-frontend-tag
        run: |
          FRONTEND_TAG="${{ steps.set-tags.outputs.frontend_tags }}"
          echo "image_tag=$FRONTEND_TAG" >> $GITHUB_OUTPUT

      - name: Run Trivy vulnerability scanner for frontend
        uses: aquasecurity/trivy-action@master
        if: steps.extract-frontend-tag.outcome == 'success'
        continue-on-error: true
        with:
          image-ref: ${{ steps.extract-frontend-tag.outputs.image_tag }}
          format: 'sarif'
          output: 'trivy-frontend-results.sarif'
          severity: 'CRITICAL,HIGH'
          exit-code: '0'  # Don't fail the build, just report
          ignore-unfixed: true

      - name: Upload backend Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v3
        if: always() && steps.extract-backend-tag.outcome == 'success'
        continue-on-error: true
        with:
          sarif_file: 'trivy-backend-results.sarif'
          category: 'backend-image'

      - name: Upload frontend Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v3
        if: always() && steps.extract-frontend-tag.outcome == 'success'
        continue-on-error: true
        with:
          sarif_file: 'trivy-frontend-results.sarif'
          category: 'frontend-image'

      - name: Comment on commit
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const sha = context.sha;
            const shortSha = sha.substring(0, 7);
            const version = '${{ needs.bump-version.outputs.new_version }}';
            const bumpType = '${{ needs.bump-version.outputs.bump_type }}';
            const env = '${{ inputs.environment }}';
            const prefix = '${{ inputs.image_tag_prefix }}' ? '${{ inputs.image_tag_prefix }}-' : '';

            let bumpMessage = '';
            if (bumpType === 'major') {
              bumpMessage = 'Major version bump';
            } else if (bumpType === 'minor') {
              bumpMessage = 'Minor version bump';
            } else {
              bumpMessage = 'Patch version bump';
            }

            const envName = env === 'stg' ? 'Staging' : 'Production';
            let imageTagsMessage = '';

            const repoOwner = context.repo.owner.toLowerCase();
            const repoName = context.repo.repo.toLowerCase();

            if (env === 'stg') {
              // For staging: both use stg-SHA
              // Using single quotes to avoid JSON escaping issues with backticks
              imageTagsMessage = 'Images are available at:\n- `ghcr.io/${context.repo.owner.toLowerCase()}/${context.repo.repo.toLowerCase()}-backend:${prefix}stg-${shortSha}`\n- `ghcr.io/${context.repo.owner.toLowerCase()}/${context.repo.repo.toLowerCase()}-frontend:${prefix}stg-${shortSha}`';
            } else {
              // For production: both use version
              // Using single quotes to avoid JSON escaping issues with backticks
              imageTagsMessage = 'Images are available at:\n- `ghcr.io/${context.repo.owner.toLowerCase()}/${context.repo.repo.toLowerCase()}-backend:${prefix}${version}`\n- `ghcr.io/${context.repo.owner.toLowerCase()}/${context.repo.repo.toLowerCase()}-frontend:${prefix}${version}`';
            }

            github.rest.repos.createCommitComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              commit_sha: sha,
              body: `## ${envName} Images Built and Pushed\n\n${bumpMessage}: **${version}**\n\n${imageTagsMessage}\n\n### Security Scanning\nVulnerability scanning was performed on both images. Check the GitHub Security tab for detailed results.\n\n### Image Labels\nImages include standard OCI labels for better traceability and management.`
            });
