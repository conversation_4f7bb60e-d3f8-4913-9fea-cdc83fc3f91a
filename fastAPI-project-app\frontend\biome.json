{"$schema": "https://biomejs.dev/schemas/1.6.1/schema.json", "organizeImports": {"enabled": true}, "files": {"ignore": ["node_modules", "src/routeTree.gen.ts", "src/client/sdk.gen.ts", "src/client/types.gen.ts", "src/client/core/**", "dist/**", "playwright.config.ts", "playwright-report", "coverage/**"]}, "linter": {"enabled": true, "rules": {"recommended": true, "suspicious": {"noExplicitAny": "off", "noArrayIndexKey": "off"}, "style": {"noNonNullAssertion": "off"}}}, "formatter": {"indentStyle": "space"}, "javascript": {"formatter": {"quoteStyle": "double", "semicolons": "asNeeded"}}}