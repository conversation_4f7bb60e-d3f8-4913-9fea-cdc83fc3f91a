# .env files
*.env
!.env.*example
!bootstrap/.env.*example
!tests/.env.*example
!bootstrap/environments/aws/.env.aws.example
!bootstrap/environments/localstack/.env.local.example

# Specific .env files to ignore
bootstrap/.env.base
bootstrap/.env.bootstrap
bootstrap/environments/aws/.env.aws
bootstrap/environments/localstack/.env.local
tests/.env.test
tests/.env.local-test

# AWS CLI installation files
awscliv2.zip
terraform_*_linux_amd64.zip

# Terraform files
**/.terraform/*
*.tfstate
*.tfstate.*
*.tfplan
terraform-docs.tar.gz

# Terraform variables and secrets
*.tfvars
*.tfvars.json
!*.tfvars.example
!terraform.tfvars.example

# Terraform override files
override.tf
override.tf.json
*_override.tf
*_override.tf.json

# Terraform lock files
.terraform.lock.hcl

# Crash log files
crash.log
crash.*.log

# Ignore CLI configuration files
.terraformrc
terraform.rc

# Temporary files
*.log
workflow_logs.txt
.output.txt
*.bak
*.swp
*.swo
.DS_Store
Thumbs.db

# IDE and editor files
.vscode/
.idea/
*.iml
*.sublime-workspace

# Python related
venv/
__pycache__/
*.pyc

# Generated files
*.json
!package.json
!package-lock.json
!*.example.json


# AI Assistant files
.codebuddy
