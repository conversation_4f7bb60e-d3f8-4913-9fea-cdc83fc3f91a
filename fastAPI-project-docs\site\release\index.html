
<!doctype html>
<html lang="en" class="no-js">
  <head>
    
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width,initial-scale=1">
      
        <meta name="description" content="Documentation for the FastAPI Project">
      
      
        <meta name="author" content="datascientest-fastAPI-project-group-25">
      
      
      
        <link rel="prev" href="../app/">
      
      
        <link rel="next" href="../infra/">
      
      
      <link rel="icon" href="../assets/images/favicon.png">
      <meta name="generator" content="mkdocs-1.5.3, mkdocs-material-9.5.3">
    
    
      
        <title>Overview - FastAPI Project Documentation</title>
      
    
    
      <link rel="stylesheet" href="../assets/stylesheets/main.50c56a3b.min.css">
      
        
        <link rel="stylesheet" href="../assets/stylesheets/palette.06af60db.min.css">
      
      


    
    
      
    
    
      
        
        
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,700,700i%7CRoboto+Mono:400,400i,700,700i&display=fallback">
        <style>:root{--md-text-font:"Roboto";--md-code-font:"Roboto Mono"}</style>
      
    
    
    <script>__md_scope=new URL("..",location),__md_hash=e=>[...e].reduce((e,_)=>(e<<5)-e+_.charCodeAt(0),0),__md_get=(e,_=localStorage,t=__md_scope)=>JSON.parse(_.getItem(t.pathname+"."+e)),__md_set=(e,_,t=localStorage,a=__md_scope)=>{try{t.setItem(a.pathname+"."+e,JSON.stringify(_))}catch(e){}}</script>
    
      

    
    
    
  </head>
  
  
    
    
    
    
    
    <body dir="ltr" data-md-color-scheme="default" data-md-color-primary="indigo" data-md-color-accent="indigo">
  
    
    <input class="md-toggle" data-md-toggle="drawer" type="checkbox" id="__drawer" autocomplete="off">
    <input class="md-toggle" data-md-toggle="search" type="checkbox" id="__search" autocomplete="off">
    <label class="md-overlay" for="__drawer"></label>
    <div data-md-component="skip">
      
        
        <a href="#fastapi-project-release" class="md-skip">
          Skip to content
        </a>
      
    </div>
    <div data-md-component="announce">
      
    </div>
    
    
      

<header class="md-header" data-md-component="header">
  <nav class="md-header__inner md-grid" aria-label="Header">
    <a href=".." title="FastAPI Project Documentation" class="md-header__button md-logo" aria-label="FastAPI Project Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54Z"/></svg>

    </a>
    <label class="md-header__button md-icon" for="__drawer">
      
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M3 6h18v2H3V6m0 5h18v2H3v-2m0 5h18v2H3v-2Z"/></svg>
    </label>
    <div class="md-header__title" data-md-component="header-title">
      <div class="md-header__ellipsis">
        <div class="md-header__topic">
          <span class="md-ellipsis">
            FastAPI Project Documentation
          </span>
        </div>
        <div class="md-header__topic" data-md-component="header-topic">
          <span class="md-ellipsis">
            
              Overview
            
          </span>
        </div>
      </div>
    </div>
    
      
    
    
    
    
      <label class="md-header__button md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5Z"/></svg>
      </label>
      <div class="md-search" data-md-component="search" role="dialog">
  <label class="md-search__overlay" for="__search"></label>
  <div class="md-search__inner" role="search">
    <form class="md-search__form" name="search">
      <input type="text" class="md-search__input" name="query" aria-label="Search" placeholder="Search" autocapitalize="off" autocorrect="off" autocomplete="off" spellcheck="false" data-md-component="search-query" required>
      <label class="md-search__icon md-icon" for="__search">
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M9.5 3A6.5 6.5 0 0 1 16 9.5c0 1.61-.59 3.09-1.56 4.23l.27.27h.79l5 5-1.5 1.5-5-5v-.79l-.27-.27A6.516 6.516 0 0 1 9.5 16 6.5 6.5 0 0 1 3 9.5 6.5 6.5 0 0 1 9.5 3m0 2C7 5 5 7 5 9.5S7 14 9.5 14 14 12 14 9.5 12 5 9.5 5Z"/></svg>
        
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M20 11v2H8l5.5 5.5-1.42 1.42L4.16 12l7.92-7.92L13.5 5.5 8 11h12Z"/></svg>
      </label>
      <nav class="md-search__options" aria-label="Search">
        
        <button type="reset" class="md-search__icon md-icon" title="Clear" aria-label="Clear" tabindex="-1">
          
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 6.41 17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41Z"/></svg>
        </button>
      </nav>
      
        <div class="md-search__suggest" data-md-component="search-suggest"></div>
      
    </form>
    <div class="md-search__output">
      <div class="md-search__scrollwrap" data-md-scrollfix>
        <div class="md-search-result" data-md-component="search-result">
          <div class="md-search-result__meta">
            Initializing search
          </div>
          <ol class="md-search-result__list" role="presentation"></ol>
        </div>
      </div>
    </div>
  </div>
</div>
    
    
      <div class="md-header__source">
        <a href="https://github.com/datascientest-fastAPI-project-group-25/fastAPI-project-docs" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><!--! Font Awesome Free 6.5.1 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2023 Fonticons, Inc.--><path d="M439.55 236.05 244 40.45a28.87 28.87 0 0 0-40.81 0l-40.66 40.63 51.52 51.52c27.06-9.14 52.68 16.77 43.39 43.68l49.66 49.66c34.23-11.8 61.18 31 35.47 56.69-26.49 26.49-70.21-2.87-56-37.34L240.22 199v121.85c25.3 12.54 22.26 41.85 9.08 55a34.34 34.34 0 0 1-48.55 0c-17.57-17.6-11.07-46.91 11.25-56v-123c-20.8-8.51-24.6-30.74-18.64-45L142.57 101 8.45 235.14a28.86 28.86 0 0 0 0 40.81l195.61 195.6a28.86 28.86 0 0 0 40.8 0l194.69-194.69a28.86 28.86 0 0 0 0-40.81z"/></svg>
  </div>
  <div class="md-source__repository">
    GitHub
  </div>
</a>
      </div>
    
  </nav>
  
</header>
    
    <div class="md-container" data-md-component="container">
      
      
        
          
            
<nav class="md-tabs" aria-label="Tabs" data-md-component="tabs">
  <div class="md-grid">
    <ul class="md-tabs__list">
      
        
  
  
  
    <li class="md-tabs__item">
      <a href=".." class="md-tabs__link">
        
  
    
  
  Home

      </a>
    </li>
  

      
        
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../app/" class="md-tabs__link">
          
  
  App

        </a>
      </li>
    
  

      
        
  
  
    
  
  
    
    
      <li class="md-tabs__item md-tabs__item--active">
        <a href="./" class="md-tabs__link">
          
  
  Release

        </a>
      </li>
    
  

      
        
  
  
  
    
    
      <li class="md-tabs__item">
        <a href="../infra/" class="md-tabs__link">
          
  
  Infrastructure

        </a>
      </li>
    
  

      
    </ul>
  </div>
</nav>
          
        
      
      <main class="md-main" data-md-component="main">
        <div class="md-main__inner md-grid">
          
            
              
              <div class="md-sidebar md-sidebar--primary" data-md-component="sidebar" data-md-type="navigation" >
                <div class="md-sidebar__scrollwrap">
                  <div class="md-sidebar__inner">
                    


  


  

<nav class="md-nav md-nav--primary md-nav--lifted md-nav--integrated" aria-label="Navigation" data-md-level="0">
  <label class="md-nav__title" for="__drawer">
    <a href=".." title="FastAPI Project Documentation" class="md-nav__button md-logo" aria-label="FastAPI Project Documentation" data-md-component="logo">
      
  
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M12 8a3 3 0 0 0 3-3 3 3 0 0 0-3-3 3 3 0 0 0-3 3 3 3 0 0 0 3 3m0 3.54C9.64 9.35 6.5 8 3 8v11c3.5 0 6.64 1.35 9 3.54 2.36-2.19 5.5-3.54 9-3.54V8c-3.5 0-6.64 1.35-9 3.54Z"/></svg>

    </a>
    FastAPI Project Documentation
  </label>
  
    <div class="md-nav__source">
      <a href="https://github.com/datascientest-fastAPI-project-group-25/fastAPI-project-docs" title="Go to repository" class="md-source" data-md-component="source">
  <div class="md-source__icon md-icon">
    
    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><!--! Font Awesome Free 6.5.1 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License) Copyright 2023 Fonticons, Inc.--><path d="M439.55 236.05 244 40.45a28.87 28.87 0 0 0-40.81 0l-40.66 40.63 51.52 51.52c27.06-9.14 52.68 16.77 43.39 43.68l49.66 49.66c34.23-11.8 61.18 31 35.47 56.69-26.49 26.49-70.21-2.87-56-37.34L240.22 199v121.85c25.3 12.54 22.26 41.85 9.08 55a34.34 34.34 0 0 1-48.55 0c-17.57-17.6-11.07-46.91 11.25-56v-123c-20.8-8.51-24.6-30.74-18.64-45L142.57 101 8.45 235.14a28.86 28.86 0 0 0 0 40.81l195.61 195.6a28.86 28.86 0 0 0 40.8 0l194.69-194.69a28.86 28.86 0 0 0 0-40.81z"/></svg>
  </div>
  <div class="md-source__repository">
    GitHub
  </div>
</a>
    </div>
  
  <ul class="md-nav__list" data-md-scrollfix>
    
      
      
  
  
  
  
    <li class="md-nav__item">
      <a href=".." class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Home
  </span>
  

      </a>
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
      
      
    
    <li class="md-nav__item md-nav__item--section md-nav__item--nested">
      
        
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_2" >
        
          
          <label class="md-nav__link" for="__nav_2" id="__nav_2_label" tabindex="">
            
  
  <span class="md-ellipsis">
    App
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_2_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_2">
            <span class="md-nav__icon md-icon"></span>
            App
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../app/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Overview
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
    
  
  
  
    
    
    
    
    
      
      
    
    <li class="md-nav__item md-nav__item--active md-nav__item--section md-nav__item--nested">
      
        
        
        
        <input class="md-nav__toggle md-toggle " type="checkbox" id="__nav_3" checked>
        
          
          <label class="md-nav__link" for="__nav_3" id="__nav_3_label" tabindex="">
            
  
  <span class="md-ellipsis">
    Release
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_3_label" aria-expanded="true">
          <label class="md-nav__title" for="__nav_3">
            <span class="md-nav__icon md-icon"></span>
            Release
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
    
  
  
  
    <li class="md-nav__item md-nav__item--active">
      
      <input class="md-nav__toggle md-toggle" type="checkbox" id="__toc">
      
      
        
      
      
        <label class="md-nav__link md-nav__link--active" for="__toc">
          
  
  <span class="md-ellipsis">
    Overview
  </span>
  

          <span class="md-nav__icon md-icon"></span>
        </label>
      
      <a href="./" class="md-nav__link md-nav__link--active">
        
  
  <span class="md-ellipsis">
    Overview
  </span>
  

      </a>
      
        

<nav class="md-nav md-nav--secondary" aria-label="Table of contents">
  
  
  
    
  
  
    <label class="md-nav__title" for="__toc">
      <span class="md-nav__icon md-icon"></span>
      Table of contents
    </label>
    <ul class="md-nav__list" data-md-component="toc" data-md-scrollfix>
      
        <li class="md-nav__item">
  <a href="#documentation" class="md-nav__link">
    <span class="md-ellipsis">
      Documentation
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#repository-structure" class="md-nav__link">
    <span class="md-ellipsis">
      Repository Structure
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#prerequisites" class="md-nav__link">
    <span class="md-ellipsis">
      Prerequisites
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#release-strategy" class="md-nav__link">
    <span class="md-ellipsis">
      Release Strategy
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#environment-overview" class="md-nav__link">
    <span class="md-ellipsis">
      Environment Overview
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Environment Overview">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#development" class="md-nav__link">
    <span class="md-ellipsis">
      Development
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#staging" class="md-nav__link">
    <span class="md-ellipsis">
      Staging
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#production" class="md-nav__link">
    <span class="md-ellipsis">
      Production
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#deployment-methods" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Methods
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Deployment Methods">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#using-scripts" class="md-nav__link">
    <span class="md-ellipsis">
      Using Scripts
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#using-docker-platform-agnostic-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Using Docker (Platform-Agnostic Setup)
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#using-github-actions" class="md-nav__link">
    <span class="md-ellipsis">
      Using GitHub Actions
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#deployment-process" class="md-nav__link">
    <span class="md-ellipsis">
      Deployment Process
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Deployment Process">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#initial-setup" class="md-nav__link">
    <span class="md-ellipsis">
      Initial Setup
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#argocd-integration" class="md-nav__link">
    <span class="md-ellipsis">
      ArgoCD Integration
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#configuration" class="md-nav__link">
    <span class="md-ellipsis">
      Configuration
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Configuration">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#image-tags" class="md-nav__link">
    <span class="md-ellipsis">
      Image Tags
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#resource-configurations" class="md-nav__link">
    <span class="md-ellipsis">
      Resource Configurations
    </span>
  </a>
  
    <nav class="md-nav" aria-label="Resource Configurations">
      <ul class="md-nav__list">
        
          <li class="md-nav__item">
  <a href="#development_1" class="md-nav__link">
    <span class="md-ellipsis">
      Development
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#staging_1" class="md-nav__link">
    <span class="md-ellipsis">
      Staging
    </span>
  </a>
  
</li>
        
          <li class="md-nav__item">
  <a href="#production_1" class="md-nav__link">
    <span class="md-ellipsis">
      Production
    </span>
  </a>
  
</li>
        
      </ul>
    </nav>
  
</li>
        
      </ul>
    </nav>
  
</li>
      
        <li class="md-nav__item">
  <a href="#security-considerations" class="md-nav__link">
    <span class="md-ellipsis">
      Security Considerations
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#monitoring" class="md-nav__link">
    <span class="md-ellipsis">
      Monitoring
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#contributing" class="md-nav__link">
    <span class="md-ellipsis">
      Contributing
    </span>
  </a>
  
</li>
      
        <li class="md-nav__item">
  <a href="#support" class="md-nav__link">
    <span class="md-ellipsis">
      Support
    </span>
  </a>
  
</li>
      
    </ul>
  
</nav>
      
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
      
      
  
  
  
  
    
    
    
    
    
      
      
    
    <li class="md-nav__item md-nav__item--section md-nav__item--nested">
      
        
        
        
          
        
        <input class="md-nav__toggle md-toggle md-toggle--indeterminate" type="checkbox" id="__nav_4" >
        
          
          <label class="md-nav__link" for="__nav_4" id="__nav_4_label" tabindex="">
            
  
  <span class="md-ellipsis">
    Infrastructure
  </span>
  

            <span class="md-nav__icon md-icon"></span>
          </label>
        
        <nav class="md-nav" data-md-level="1" aria-labelledby="__nav_4_label" aria-expanded="false">
          <label class="md-nav__title" for="__nav_4">
            <span class="md-nav__icon md-icon"></span>
            Infrastructure
          </label>
          <ul class="md-nav__list" data-md-scrollfix>
            
              
                
  
  
  
  
    <li class="md-nav__item">
      <a href="../infra/" class="md-nav__link">
        
  
  <span class="md-ellipsis">
    Overview
  </span>
  

      </a>
    </li>
  

              
            
          </ul>
        </nav>
      
    </li>
  

    
  </ul>
</nav>
                  </div>
                </div>
              </div>
            
            
          
          
            <div class="md-content" data-md-component="content">
              <article class="md-content__inner md-typeset">
                
                  

  
  


<h1 id="fastapi-project-release">FastAPI Project Release<a class="headerlink" href="#fastapi-project-release" title="Permanent link">&para;</a></h1>
<p>This repository contains the Kubernetes manifests, Helm charts, and Argo CD configurations for deploying the FastAPI application.</p>
<h2 id="documentation">Documentation<a class="headerlink" href="#documentation" title="Permanent link">&para;</a></h2>
<p>Detailed documentation is available in the <code>docs/</code> directory:</p>
<ul>
<li><a href="docs/release-strategy.md">Release Strategy</a> - Comprehensive guide to the release strategy</li>
<li><a href="docs/quick-start.md">Quick Start Guide</a> - Quick start guide for new team members</li>
<li><a href="docs/troubleshooting.md">Troubleshooting</a> - Solutions to common issues</li>
</ul>
<h2 id="repository-structure">Repository Structure<a class="headerlink" href="#repository-structure" title="Permanent link">&para;</a></h2>
<div class="highlight"><pre><span></span><code>.
├── charts/                    # Helm charts
│   └── fastapi/              # FastAPI application Helm chart
│       ├── Chart.yaml        # Chart metadata
│       ├── values.yaml       # Default values
│       └── templates/        # Helm templates
│           ├── _helpers.tpl
│           ├── backend-deployment.yaml
│           ├── configmap.yaml
│           ├── frontend-deployment.yaml
│           ├── ingress.yaml
│           ├── postgres-statefulset.yaml
│           ├── services.yaml
│           └── db-init-script-configmap.yaml  # Database initialization and migrations
├── config/                   # Environment-specific configurations
│   ├── argocd/              # Argo CD Application manifests
│   │   ├── staging.yaml     # Staging environment
│   │   └── production.yaml  # Production environment
│   └── helm/                # Environment-specific Helm values
│       ├── values.yaml      # Default values (development)
│       ├── staging.yaml     # Staging environment
│       └── production.yaml  # Production environment
├── scripts/                  # Deployment and maintenance scripts
│   ├── deploy-dev.sh        # Development deployment script
│   ├── deploy-prod.sh       # Production deployment script
│   ├── cleanup.sh           # Environment cleanup script
│   ├── setup-argocd.sh      # ArgoCD setup script
│   └── setup-argocd-integration.sh # ArgoCD CI/CD integration script
└── .github/                 # GitHub Actions workflows
    └── workflows/
        ├── helm-deploy.yml  # Deployment workflow
        ├── helm-test.yml    # Helm chart testing workflow
        ├── pr-automation.yml # PR automation workflow with branch deletion on merge
        ├── helm-argocd-test.yml # ArgoCD configuration testing workflow
        └── argocd-integration.yml # ArgoCD integration workflow
</code></pre></div>
<h2 id="prerequisites">Prerequisites<a class="headerlink" href="#prerequisites" title="Permanent link">&para;</a></h2>
<p>The project uses several tools that will be automatically checked and installed as needed. To begin:</p>
<div class="highlight"><pre><span></span><code>make<span class="w"> </span>init
</code></pre></div>
<p>This command will:
1. Detect your operating system (macOS, Linux, or Windows)
2. Install Bun (JavaScript/TypeScript runtime) if not present
3. Check for and help you install other required tools:
   - Git (Version control)
   - Docker (Container runtime)
   - Kubectl (Kubernetes CLI)
   - k3d (Local Kubernetes)
   - Helm (Kubernetes package manager)</p>
<p>The initialization process is platform-aware and will provide appropriate installation instructions for your system.</p>
<p>For macOS users with Homebrew, you can install all required tools with:
<div class="highlight"><pre><span></span><code>brew<span class="w"> </span>install<span class="w"> </span>git<span class="w"> </span>docker<span class="w"> </span>kubectl<span class="w"> </span>k3d<span class="w"> </span>helm
</code></pre></div></p>
<p>You'll also need:
- Kubernetes cluster
- Argo CD installed
- GitHub Container Registry access</p>
<h2 id="release-strategy">Release Strategy<a class="headerlink" href="#release-strategy" title="Permanent link">&para;</a></h2>
<p>This project follows a streamlined release strategy with feature/fix branches that merge directly into the main branch:</p>
<ol>
<li><strong>Development Workflow</strong>:</li>
<li>Create feature/fix branches from main (<code>feat/*</code> or <code>fix/*</code>)</li>
<li>Push changes to GitHub to automatically create a PR</li>
<li>PR triggers tests and validation workflows</li>
<li>
<p>After review and approval, merge to main</p>
</li>
<li>
<p><strong>Deployment Process</strong>:</p>
</li>
<li>Main branch changes trigger deployment to staging</li>
<li>After validation in staging, promote to production</li>
<li>
<p>ArgoCD manages the deployment process</p>
</li>
<li>
<p><strong>Environments</strong>:</p>
</li>
<li><strong>Staging</strong>: Pre-production environment for validation</li>
<li><strong>Production</strong>: Live environment</li>
</ol>
<h2 id="environment-overview">Environment Overview<a class="headerlink" href="#environment-overview" title="Permanent link">&para;</a></h2>
<h3 id="development">Development<a class="headerlink" href="#development" title="Permanent link">&para;</a></h3>
<ul>
<li>Branch: <code>main</code></li>
<li>Values: <code>config/helm/values.yaml</code></li>
<li>Features:</li>
<li>Debug mode enabled</li>
<li>Minimal resources</li>
<li>Local development optimized</li>
</ul>
<h3 id="staging">Staging<a class="headerlink" href="#staging" title="Permanent link">&para;</a></h3>
<ul>
<li>Branch: <code>stg</code></li>
<li>Values: <code>config/helm/staging.yaml</code></li>
<li>Features:</li>
<li>Debugging enabled</li>
<li>Moderate resource limits</li>
<li>Automated deployments</li>
<li>Single replica per service</li>
</ul>
<h3 id="production">Production<a class="headerlink" href="#production" title="Permanent link">&para;</a></h3>
<ul>
<li>Branch: <code>main</code></li>
<li>Values: <code>config/helm/production.yaml</code></li>
<li>Features:</li>
<li>Debugging disabled</li>
<li>High resource limits</li>
<li>Multiple replicas</li>
<li>Autoscaling enabled</li>
<li>Enhanced security</li>
<li>TLS enabled</li>
</ul>
<h2 id="deployment-methods">Deployment Methods<a class="headerlink" href="#deployment-methods" title="Permanent link">&para;</a></h2>
<h3 id="using-scripts">Using Scripts<a class="headerlink" href="#using-scripts" title="Permanent link">&para;</a></h3>
<p>The repository includes several utility scripts to manage deployments:</p>
<div class="highlight"><pre><span></span><code><span class="c1"># Deploy to development environment</span>
./scripts/deploy-dev.sh

<span class="c1"># Deploy to production environment</span>
./scripts/deploy-prod.sh

<span class="c1"># Clean up environments</span>
./scripts/cleanup.sh<span class="w"> </span>dev<span class="w">    </span><span class="c1"># Clean development environment</span>
./scripts/cleanup.sh<span class="w"> </span>prod<span class="w">   </span><span class="c1"># Clean production environment</span>
./scripts/cleanup.sh<span class="w"> </span>all<span class="w">    </span><span class="c1"># Clean all environments</span>
</code></pre></div>
<h3 id="using-docker-platform-agnostic-setup">Using Docker (Platform-Agnostic Setup)<a class="headerlink" href="#using-docker-platform-agnostic-setup" title="Permanent link">&para;</a></h3>
<p>For a consistent setup experience across different platforms (Linux, macOS, Windows), you can use the Docker-based setup:</p>
<div class="highlight"><pre><span></span><code><span class="c1"># Set up a local k3d cluster using Docker</span>
make<span class="w"> </span>setup-k3d-docker
</code></pre></div>
<p>This method uses a Docker container that includes all the necessary tools (k3d, kubectl, Helm) and runs the setup script inside the container. This approach ensures that the setup process is consistent regardless of the host operating system.</p>
<p>Requirements:
- Docker
- Docker Compose</p>
<p>The Docker-based setup automatically:
1. Builds a container with all required tools
2. Sets up a local k3d cluster
3. Installs ArgoCD
4. Configures the necessary components</p>
<h3 id="using-github-actions">Using GitHub Actions<a class="headerlink" href="#using-github-actions" title="Permanent link">&para;</a></h3>
<p>The project uses GitHub Actions for CI/CD with the following workflows:</p>
<ol>
<li><strong>PR Automation (<code>pr-automation.yml</code>)</strong></li>
<li>Triggers on pushes to feature/<em> and fix/</em> branches</li>
<li>Automatically creates a PR if one doesn't exist</li>
<li>Adds appropriate labels and descriptions</li>
<li>
<p>Deletes branches automatically after PR is merged</p>
</li>
<li>
<p><strong>ArgoCD Configuration Tests (<code>helm-argocd-test.yml</code>)</strong></p>
</li>
<li>Triggers on PR creation and updates affecting ArgoCD configurations</li>
<li>Validates ArgoCD application manifests</li>
<li>Prepares deployment manifests for testing</li>
<li>
<p>Focuses specifically on ArgoCD-related configurations</p>
</li>
<li>
<p><strong>Helm Chart Test (<code>helm-test.yml</code>)</strong></p>
</li>
<li>Triggers on pull requests and pushes to main</li>
<li>Validates Helm charts across all environments</li>
<li>Runs comprehensive chart-testing</li>
<li>
<p>Tests chart installation in a Kind cluster</p>
</li>
<li>
<p><strong>Helm Chart Deploy (<code>helm-deploy.yml</code>)</strong></p>
</li>
<li>Deploys to development, staging, and production environments</li>
<li>Supports manual triggering with environment selection</li>
<li>Includes validation and verification steps</li>
<li>
<p>Manages environment-specific configurations</p>
</li>
<li>
<p><strong>ArgoCD Integration (<code>argocd-integration.yml</code>)</strong></p>
</li>
<li>Manually triggered workflow for ArgoCD integration</li>
<li>Configures ArgoCD applications for different environments</li>
<li>Manages deployment synchronization</li>
<li>Provides deployment status and URLs</li>
</ol>
<h2 id="deployment-process">Deployment Process<a class="headerlink" href="#deployment-process" title="Permanent link">&para;</a></h2>
<ol>
<li>Images are built and pushed to GitHub Container Registry (ghcr.io/datascientest-fastapi-project-group-25)</li>
<li>CI pipeline updates image tags in the appropriate values file</li>
<li>Argo CD detects changes and syncs the application</li>
</ol>
<h3 id="initial-setup">Initial Setup<a class="headerlink" href="#initial-setup" title="Permanent link">&para;</a></h3>
<ol>
<li>
<p>Install Argo CD:
   <div class="highlight"><pre><span></span><code>kubectl<span class="w"> </span>create<span class="w"> </span>namespace<span class="w"> </span>argocd
kubectl<span class="w"> </span>apply<span class="w"> </span>-n<span class="w"> </span>argocd<span class="w"> </span>-f<span class="w"> </span>https://raw.githubusercontent.com/argoproj/argo-cd/stable/manifests/install.yaml
</code></pre></div></p>
</li>
<li>
<p>Configure GitHub Container Registry credentials:
   <div class="highlight"><pre><span></span><code>kubectl<span class="w"> </span>create<span class="w"> </span>secret<span class="w"> </span>docker-registry<span class="w"> </span>ghcr-secret<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--docker-server<span class="o">=</span>ghcr.io<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--docker-username<span class="o">=</span>&lt;github-username&gt;<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--docker-password<span class="o">=</span>&lt;github-pat&gt;<span class="w"> </span><span class="se">\</span>
<span class="w">  </span>--namespace<span class="o">=</span>fastapi-helm
</code></pre></div></p>
</li>
<li>
<p>Set up ArgoCD API key for CI/CD integration and store it as a GitHub secret:
   <div class="highlight"><pre><span></span><code><span class="c1"># Run the setup script to configure ArgoCD, generate an API key, and store it as a GitHub secret</span>
./scripts/setup-argocd-github.sh
</code></pre></div></p>
</li>
<li>
<p>Apply Argo CD applications:
   <div class="highlight"><pre><span></span><code><span class="c1"># For staging</span>
kubectl<span class="w"> </span>apply<span class="w"> </span>-f<span class="w"> </span>config/argocd/staging.yaml

<span class="c1"># For production</span>
kubectl<span class="w"> </span>apply<span class="w"> </span>-f<span class="w"> </span>config/argocd/production.yaml
</code></pre></div></p>
</li>
</ol>
<h3 id="argocd-integration">ArgoCD Integration<a class="headerlink" href="#argocd-integration" title="Permanent link">&para;</a></h3>
<p>The repository includes scripts and workflows for ArgoCD integration:</p>
<ol>
<li><strong>Manual Setup</strong>:</li>
<li>Use <code>./scripts/setup-argocd-github.sh</code> to install and configure ArgoCD</li>
<li>Generate an API key for CI/CD integration</li>
<li>
<p>Automatically store the API key and server URL as GitHub secrets (<code>ARGOCD_AUTH_TOKEN</code> and <code>ARGOCD_SERVER</code>)</p>
</li>
<li>
<p><strong>CI/CD Integration</strong>:</p>
</li>
<li>The <code>argocd-integration.yml</code> workflow configures ArgoCD applications</li>
<li>Main branch changes trigger deployment to staging</li>
<li>
<p>After validation, changes can be promoted to production</p>
</li>
<li>
<p><strong>PR Testing</strong>:</p>
</li>
<li>When a PR is created, the <code>helm-argocd-test.yml</code> workflow validates Helm charts and ArgoCD configurations</li>
<li>The workflow prepares deployment manifests for testing</li>
<li>These manifests can be used for manual testing or review</li>
</ol>
<h2 id="configuration">Configuration<a class="headerlink" href="#configuration" title="Permanent link">&para;</a></h2>
<h3 id="image-tags">Image Tags<a class="headerlink" href="#image-tags" title="Permanent link">&para;</a></h3>
<ul>
<li>Development: <code>dev-latest</code> or <code>dev-[commit-sha]</code></li>
<li>Staging: <code>staging-latest</code> or <code>staging-[commit-sha]</code></li>
<li>Production: <code>production-latest</code> or <code>production-[commit-sha]</code></li>
</ul>
<h3 id="resource-configurations">Resource Configurations<a class="headerlink" href="#resource-configurations" title="Permanent link">&para;</a></h3>
<h4 id="development_1">Development<a class="headerlink" href="#development_1" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="nt">resources</span><span class="p">:</span>
<span class="w">  </span><span class="nt">limits</span><span class="p">:</span>
<span class="w">    </span><span class="nt">cpu</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">200m</span>
<span class="w">    </span><span class="nt">memory</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">256Mi</span>
<span class="w">  </span><span class="nt">requests</span><span class="p">:</span>
<span class="w">    </span><span class="nt">cpu</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">100m</span>
<span class="w">    </span><span class="nt">memory</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">128Mi</span>
</code></pre></div>
<h4 id="staging_1">Staging<a class="headerlink" href="#staging_1" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="nt">resources</span><span class="p">:</span>
<span class="w">  </span><span class="nt">limits</span><span class="p">:</span>
<span class="w">    </span><span class="nt">cpu</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">500m</span>
<span class="w">    </span><span class="nt">memory</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">512Mi</span>
<span class="w">  </span><span class="nt">requests</span><span class="p">:</span>
<span class="w">    </span><span class="nt">cpu</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">200m</span>
<span class="w">    </span><span class="nt">memory</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">256Mi</span>
</code></pre></div>
<h4 id="production_1">Production<a class="headerlink" href="#production_1" title="Permanent link">&para;</a></h4>
<div class="highlight"><pre><span></span><code><span class="nt">resources</span><span class="p">:</span>
<span class="w">  </span><span class="nt">limits</span><span class="p">:</span>
<span class="w">    </span><span class="nt">cpu</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1000m</span>
<span class="w">    </span><span class="nt">memory</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">1024Mi</span>
<span class="w">  </span><span class="nt">requests</span><span class="p">:</span>
<span class="w">    </span><span class="nt">cpu</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">500m</span>
<span class="w">    </span><span class="nt">memory</span><span class="p">:</span><span class="w"> </span><span class="l l-Scalar l-Scalar-Plain">512Mi</span>
</code></pre></div>
<h2 id="security-considerations">Security Considerations<a class="headerlink" href="#security-considerations" title="Permanent link">&para;</a></h2>
<ul>
<li>All secrets should be managed through AWS Secrets Manager</li>
<li>Debug mode is disabled in production</li>
<li>Network policies restrict pod communication</li>
<li>TLS is enabled for production ingress</li>
<li>Pods run as non-root users</li>
<li>Resource limits are enforced</li>
<li>HPA ensures proper scaling</li>
</ul>
<h2 id="monitoring">Monitoring<a class="headerlink" href="#monitoring" title="Permanent link">&para;</a></h2>
<ul>
<li>Kubernetes metrics</li>
<li>Application health checks</li>
<li>Resource utilization</li>
<li>Autoscaling behavior</li>
<li>Deployment status through Argo CD UI</li>
</ul>
<h2 id="contributing">Contributing<a class="headerlink" href="#contributing" title="Permanent link">&para;</a></h2>
<ol>
<li>Create a new branch from the target environment branch</li>
<li>Make changes to the appropriate values file</li>
<li>Create a pull request</li>
<li>After review and approval, changes will be deployed automatically</li>
</ol>
<h2 id="support">Support<a class="headerlink" href="#support" title="Permanent link">&para;</a></h2>
<p>For issues or questions, please contact DataScientest Group 25:
- GitHub: <a href="https://github.com/datascientest-fastapi-project-group-25">datascientest-fastapi-project-group-25</a></p>
<h1 id="need-to-be-updated-in-test-docs">need to be updated in test-docs<a class="headerlink" href="#need-to-be-updated-in-test-docs" title="Permanent link">&para;</a></h1>
<h1 id="test-change-for-github-actions">Test change for GitHub Actions<a class="headerlink" href="#test-change-for-github-actions" title="Permanent link">&para;</a></h1>
<h1 id="another-test-change">Another test change<a class="headerlink" href="#another-test-change" title="Permanent link">&para;</a></h1>












                
              </article>
            </div>
          
          
<script>var target=document.getElementById(location.hash.slice(1));target&&target.name&&(target.checked=target.name.startsWith("__tabbed_"))</script>
        </div>
        
      </main>
      
        <footer class="md-footer">
  
  <div class="md-footer-meta md-typeset">
    <div class="md-footer-meta__inner md-grid">
      <div class="md-copyright">
  
  
    Made with
    <a href="https://squidfunk.github.io/mkdocs-material/" target="_blank" rel="noopener">
      Material for MkDocs
    </a>
  
</div>
      
    </div>
  </div>
</footer>
      
    </div>
    <div class="md-dialog" data-md-component="dialog">
      <div class="md-dialog__inner md-typeset"></div>
    </div>
    
    
    <script id="__config" type="application/json">{"base": "..", "features": ["navigation.tabs", "navigation.sections", "navigation.expand", "toc.integrate", "search.suggest", "search.highlight", "content.code.copy"], "search": "../assets/javascripts/workers/search.f886a092.min.js", "translations": {"clipboard.copied": "Copied to clipboard", "clipboard.copy": "Copy to clipboard", "search.result.more.one": "1 more on this page", "search.result.more.other": "# more on this page", "search.result.none": "No matching documents", "search.result.one": "1 matching document", "search.result.other": "# matching documents", "search.result.placeholder": "Type to start searching", "search.result.term.missing": "Missing", "select.version": "Select version"}}</script>
    
    
      <script src="../assets/javascripts/bundle.d7c377c4.min.js"></script>
      
    
  </body>
</html>