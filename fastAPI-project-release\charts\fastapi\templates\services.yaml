# Backend Service
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.backend.service.name }}
  namespace: {{ .Values.app.namespace }}
  labels:
    app: {{ .Values.backend.name }}
spec:
  type: {{ .Values.backend.service.type }}
  ports:
    - port: {{ .Values.backend.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app: {{ .Values.backend.name }}
---
# Frontend Service
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.frontend.service.name }}
  namespace: {{ .Values.app.namespace }}
  labels:
    app: {{ .Values.frontend.name }}
spec:
  type: {{ .Values.frontend.service.type }}
  ports:
    - port: {{ .Values.frontend.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app: {{ .Values.frontend.name }}
---
# Database Service
apiVersion: v1
kind: Service
metadata:
  name: {{ .Values.database.service.name }}
  namespace: {{ .Values.app.namespace }}
  labels:
    app: {{ .Values.database.name }}
spec:
  type: ClusterIP
  ports:
    - port: {{ .Values.database.service.port }}
      targetPort: postgres
      protocol: TCP
      name: postgres
  selector:
    app: {{ .Values.database.name }}