{"compilerOptions": {"target": "ESNext", "module": "ESNext", "moduleResolution": "bundler", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "types": ["bun", "node"], "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*"]}