groups:
  - name: fastapi-application
    rules:
      # High request latency alert
      - alert: HighRequestLatency
        expr: |
          rate(fastapi_request_duration_seconds_sum[5m]) / rate(fastapi_request_duration_seconds_count[5m]) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High request latency detected"
          description: "The average request latency is above 1 second for 5 minutes."

      # High error rate alert
      - alert: HighErrorRate
        expr: |
          rate(fastapi_requests_total{status=~"5.."}[5m]) / rate(fastapi_requests_total[5m]) > 0.1
        for: 5m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "More than 10% of requests are failing with 5xx errors."

      # High memory usage alert
      - alert: HighMemoryUsage
        expr: |
          process_resident_memory_bytes > 1000000000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Application memory usage is above 1GB."

      # Low request rate alert
      - alert: LowRequestRate
        expr: |
          rate(fastapi_requests_total[5m]) < 1
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Low request rate"
          description: "Application is receiving less than 1 request per minute."

  - name: node-metrics
    rules:
      # High CPU usage
      - alert: HighCPUUsage
        expr: |
          100 - (avg by (instance) (irate(node_cpu_seconds_total{mode='idle'}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage on node"
          description: "Node CPU usage is above 80% for 5 minutes."

      # Low disk space
      - alert: LowDiskSpace
        expr: |
          (node_filesystem_size_bytes{mountpoint='/'} - node_filesystem_free_bytes{mountpoint='/'}) / node_filesystem_size_bytes{mountpoint='/'} * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Low disk space"
          description: "Disk usage is above 85%."

      # High memory usage
      - alert: HighNodeMemoryUsage
        expr: |
          (node_memory_MemTotal_bytes - node_memory_MemFree_bytes - node_memory_Buffers_bytes - node_memory_Cached_bytes) / node_memory_MemTotal_bytes * 100 > 85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage on node"
          description: "Node memory usage is above 85%."
