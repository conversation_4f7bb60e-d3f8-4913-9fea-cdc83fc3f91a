apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: fastapi-stg-secrets
  namespace: fastapi-helm-staging
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: aws-secrets
    kind: ClusterSecretStore
  target:
    name: fastapi-stg-secrets
    creationPolicy: Owner
  data:
    - secretKey: POSTGRES_USERNAME
      remoteRef:
        key: staging/postgres/credentials
        property: username
    - secretKey: POSTGRES_PASSWORD
      remoteRef:
        key: staging/postgres/credentials
        property: password
    - secretKey: POSTGRES_DB
      remoteRef:
        key: staging/postgres/credentials
        property: database
    - secretKey: APP_SECRET_KEY
      remoteRef:
        key: staging/app/secrets
        property: secretKey