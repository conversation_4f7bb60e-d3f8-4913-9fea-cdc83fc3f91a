services:
  frontend:
    build:
      context: .
      dockerfile: frontend/Dockerfile.fixed.dev
    restart: "no"
    working_dir: /app
    ports:
      - "5173:5173"
    volumes:
      - ./:/app
      - frontend-pnpm-store:/root/.local/share/pnpm/store
      - frontend-node-modules:/app/node_modules
      - frontend-frontend-node-modules:/app/frontend/node_modules
    env_file:
      - .env
    environment:
      - VITE_API_URL=http://api.localhost
      - NODE_OPTIONS=--max-old-space-size=4096
      - PNPM_HOME=/root/.local/share/pnpm
      - PATH=/root/.local/share/pnpm:$PATH
      - VITE_BACKEND_CORS_ORIGINS=["http://localhost:3000", "http://localhost:5173", "http://dashboard.localhost"]
    # Use the CMD from the Dockerfile
    extra_hosts:
      - "host.docker.internal:host-gateway"

volumes:
  frontend-node-modules:
  frontend-frontend-node-modules:
  frontend-pnpm-store:
