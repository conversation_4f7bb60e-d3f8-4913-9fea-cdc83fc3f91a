apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: fastapi-app
  namespace: argocd
spec:
  project: default
  source:
    repoURL: https://github.com/datascientest-fastapi-project-group-25/fastAPI-project-release.git
    targetRevision: automation  # The branch that will be monitored
    path: fastapi-helm
    helm:
      valueFiles:
        - values.yaml
  destination:
    server: https://kubernetes.default.svc
    namespace: fastapi-helm
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true

---
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: fastapi-app-staging
  namespace: argocd
spec:
  project: default
  source:
    repoURL: https://github.com/datascientest-fastapi-project-group-25/fastAPI-project-release.git
    targetRevision: stg  # The staging branch
    path: fastapi-helm
    helm:
      valueFiles:
        - values.staging.yaml  # We'll create this file next
  destination:
    server: https://kubernetes.default.svc
    namespace: fastapi-helm-staging
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true