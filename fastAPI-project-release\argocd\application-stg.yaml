apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: fastapi-stg
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: default
  source:
    repoURL: https://github.com/datascientest-fastapi-project-group-25/fastAPI-project-release.git
    targetRevision: HEAD
    path: charts/fastapi
    helm:
      valueFiles:
        - ../../config/helm/staging.yaml
  destination:
    server: https://kubernetes.default.svc
    namespace: argocd-stg
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
      - PrunePropagationPolicy=foreground
      - PruneLast=true
    ignoreDifferences:
      - group: apps
        kind: Deployment
        jsonPointers:
          - /spec/replicas
      - group: autoscaling
        kind: HorizontalPodAutoscaler
        jsonPointers:
          - /status