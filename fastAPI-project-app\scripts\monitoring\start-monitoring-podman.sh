#!/bin/bash
# <PERSON>sh script to start the monitoring stack with <PERSON><PERSON>
# Works on macOS and Linux with <PERSON><PERSON>

# Navigate to the project root directory
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
cd "$PROJECT_ROOT"

echo "Starting monitoring stack with <PERSON><PERSON> from $PROJECT_ROOT..."

# Check if <PERSON><PERSON> is running
if ! podman info > /dev/null 2>&1; then
    echo "Error: <PERSON><PERSON> is not running. Please start <PERSON>dman and try again."
    exit 1
fi
echo "<PERSON><PERSON> is running."

# Create log directories if they don't exist
LOG_DIRS=("logs/backend" "logs/application")
for dir in "${LOG_DIRS[@]}"; do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        echo "Created log directory: $dir"
    fi
done

# Create Podman network if it doesn't exist
NETWORK_EXISTS=$(podman network ls --filter "name=monitoring-network" --format "{{.Name}}")
if [ -z "$NETWORK_EXISTS" ]; then
    echo "Creating Podman network: monitoring-network"
    podman network create monitoring-network
fi

# Start the monitoring stack
echo "Starting monitoring services with <PERSON><PERSON>..."
podman-compose -f docker-compose.monitoring-only.yml up -d

# Check if services are running
echo "Checking if services are running..."
SERVICES=("prometheus" "loki" "grafana")
for service in "${SERVICES[@]}"; do
    status=$(podman ps --filter "name=$service" --format "{{.Status}}")
    if [ -n "$status" ]; then
        echo -e "\033[0;32m$service is running: $status\033[0m"
    else
        echo -e "\033[0;33m$service is not running\033[0m"
    fi
done

echo "Monitoring setup complete."
echo "You can access the dashboards at:"
echo "- Grafana: http://localhost:3001"
echo "- Prometheus: http://localhost:9090"
