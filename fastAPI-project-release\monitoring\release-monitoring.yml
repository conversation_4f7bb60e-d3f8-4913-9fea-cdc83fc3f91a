# Release Monitoring Configuration
# Platform-independent configuration for monitoring releases

# Logging configuration
logging:
  level: info
  format: json
  output: stdout
  file: logs/release.log

# Metrics configuration
metrics:
  enabled: true
  endpoint: /metrics
  prefix: release_
  labels:
    environment: ${ENVIRONMENT:-development}
    service: release

# Alerting configuration
alerting:
  enabled: true
  endpoints:
    - type: slack
      url: ${SLACK_WEBHOOK_URL:-}
    - type: email
      recipients: ${ALERT_RECIPIENTS:-}

# Dashboard configuration
dashboard:
  title: Release Monitoring
  refresh_interval: 10s
  panels:
    - title: Release Status
      type: status
      query: release_status
    - title: Deployment Time
      type: gauge
      query: release_deployment_duration_seconds
    - title: Success Rate
      type: graph
      query: release_success_rate

# Integration with main monitoring stack
integration:
  loki:
    url: http://loki:3100
    labels:
      app: release
  prometheus:
    url: http://prometheus:9090
    job: release
  grafana:
    url: http://grafana:3000
    dashboard_id: release-monitoring
