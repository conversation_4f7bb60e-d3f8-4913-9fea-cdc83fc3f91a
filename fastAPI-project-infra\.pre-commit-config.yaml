repos:
  - repo: https://github.com/antonbabenko/pre-commit-terraform
    rev: v1.83.5
    hooks:
      - id: terraform_fmt
        files: ^terraform/
      - id: terraform_docs
        files: ^terraform/
      - id: terraform_tflint
        files: ^terraform/
      - id: terraform_validate
        files: ^terraform/
      - id: terraform_checkov
        files: ^terraform/

  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict
      - id: detect-private-key
      - id: detect-aws-credentials

  #- repo: https://github.com/bridgecrewio/checkov.git
   # rev: '3.0.0'
    #hooks:
     # - id: checkov
      #  args: [--soft-fail, --directory, terraform/]
