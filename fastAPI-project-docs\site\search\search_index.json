{"config": {"lang": ["en"], "separator": "[\\s\\-]+", "pipeline": ["stop<PERSON>ordFilter"]}, "docs": [{"location": "", "title": "FastAPI Project Documentation", "text": "<p>Welcome to the FastAPI Project documentation. This GitBook-style documentation site contains the README files from all repositories in the project, automatically synchronized and updated.</p>"}, {"location": "#project-repositories", "title": "Project Repositories", "text": "Repository Description App Repository The main application code with FastAPI backend and React frontend Release Repository Release management and deployment with Kubernetes and Argo CD Infrastructure Repository Infrastructure as code with Terraform"}, {"location": "#system-architecture", "title": "System Architecture", "text": "<pre><code>graph TD\n    A[Frontend - React] --&gt; B[Backend - FastAPI]\n    B --&gt; C[Database - PostgreSQL]\n    D[CI/CD Pipeline] --&gt; E[GitHub Container Registry]\n    E --&gt; F[Kubernetes Cluster]\n    F --&gt; G[ArgoCD]\n    G --&gt; H[Application Deployment]</code></pre>"}, {"location": "#about-this-documentation", "title": "About This Documentation", "text": "<p>This documentation is automatically synchronized from the README files in each repository. When a README is updated in any of the source repositories, it is automatically updated here and published as a GitBook-style site.</p>"}, {"location": "#features", "title": "Features", "text": "<ul> <li>Centralized Documentation: All project README files in one place</li> <li>Automatic Synchronization: Changes in source repositories are reflected here</li> <li>GitBook-Style Navigation: Easy-to-use navigation and search</li> <li>Mermaid Diagrams: Visual representation of architecture and workflows</li> <li>Code Highlighting: Syntax highlighting for code snippets</li> <li>Mobile-Friendly: Responsive design for all devices</li> </ul>"}, {"location": "app/", "title": "Overview", "text": ""}, {"location": "app/#devops-demo-application", "title": "🚀 DevOps Demo Application", "text": "<p>This repository contains a modern full-stack application with a FastAPI backend and React frontend, featuring a comprehensive CI/CD pipeline for AWS deployment.</p>"}, {"location": "app/#table-of-contents", "title": "📋 Table of Contents", "text": "<ul> <li>Architecture Overview</li> <li>Development Environment Setup</li> <li>Makefile for Local Setup</li> <li>Docker-based Development</li> <li>Local Development</li> <li>Development Workflow</li> <li>CI/CD Pipeline</li> <li>Documentation</li> <li>Environment Configuration</li> <li>Testing</li> <li>Troubleshooting</li> </ul>"}, {"location": "app/#architecture-overview", "title": "🏗️ Architecture Overview", "text": "<pre><code>graph TD\n    A[Frontend - React/TypeScript] --&gt; G[Traefik Reverse Proxy]\n    G --&gt; B[Backend - FastAPI]\n    B --&gt; C[(PostgreSQL Database)]\n    D[CI/CD - GitHub Actions] --&gt; E[GitHub Container Registry]\n    E --&gt; F[Deployment Environment]</code></pre> <ul> <li>Frontend: React, TypeScript, TanStack Query, Chakra UI</li> <li>Backend: FastAPI, SQLModel, Pydantic</li> <li>Database: PostgreSQL</li> <li>Infrastructure: Docker, Traefik, GitHub Container Registry (GHCR)</li> <li>CI/CD: GitHub Actions</li> <li>Build Tools: pnpm, Biome, UV (Python package manager)</li> </ul>"}, {"location": "app/#development-environment-setup", "title": "🛠️ Development Environment Setup", "text": ""}, {"location": "app/#prerequisites", "title": "Prerequisites", "text": "<ul> <li>Docker and Docker Compose</li> <li>Python (3.11+)</li> <li>UV for Python package management</li> <li>Git</li> <li>pnpm for efficient package management and faster builds</li> </ul>"}, {"location": "app/#initial-setup", "title": "Initial Setup", "text": "<ol> <li>Clone the repository</li> </ol> <pre><code>git clone https://github.com/yourusername/fastAPI-project-app.git\ncd fastAPI-project-app\n</code></pre> <ol> <li>Use the Makefile for setup</li> </ol> <pre><code># Setup the project (create .env, install dependencies)\nmake setup\n</code></pre> <p>Or manually:</p> <pre><code># Generate a secure .env file from .env.example\nmake env\n# Or manually: cp .env.example .env\n# Edit .env with your preferred settings\n</code></pre> <ol> <li>Install git hooks with pre-commit</li> </ol> <pre><code>pip install pre-commit\npre-commit install --hook-type pre-commit --hook-type commit-msg --hook-type pre-push\n</code></pre> <p>This will set up git hooks to automatically format code, run linting checks, and ensure code quality on commit.</p>"}, {"location": "app/#makefile-the-central-interface-for-all-project-tasks", "title": "🔧 Makefile - The Central Interface for All Project Tasks", "text": "<p>The Makefile is the primary and recommended way to interact with this project throughout its entire lifecycle. From initial setup and development to testing, deployment, and maintenance, all operations should be performed using the Makefile commands for consistency and efficiency.</p> <p>All team members should use these commands rather than running individual tools directly to ensure everyone follows the same workflows and processes:</p> <pre><code># Show available commands\nmake help\n\n# Setup the project (create .env, install dependencies)\nmake setup\n\n# Start Docker containers with pnpm\nmake up\n\n# Initialize the database (create tables and first superuser)\nmake init-db\n\n# Stop Docker containers\nmake down\n\n# Restart Docker containers\nmake restart\n\n# Run all tests\nmake test\n\n# Create a new feature branch\nmake feat name=branch-name\n\n# Create a new fix branch\nmake fix name=branch-name\n\n# Create a new fix branch with automerge\nmake fix-automerge name=branch-name\n</code></pre>"}, {"location": "app/#database-initialization", "title": "🗄️ Database Initialization", "text": "<p>The application automatically initializes the database when the backend container starts, creating all necessary tables and the first superuser account. This process is handled by the prestart script that runs before the FastAPI application starts.</p> <p>If you need to manually initialize or reset the database, you can use:</p> <pre><code># Initialize the database (create tables and first superuser)\nmake init-db\n</code></pre>"}, {"location": "app/#default-login-credentials", "title": "De<PERSON>ult <PERSON>gin Credentials", "text": "<p>After initialization, you can log in with:</p> <ul> <li>Email: <EMAIL></li> <li>Password: The value of <code>FIRST_SUPERUSER_PASSWORD</code> in your <code>.env</code> file</li> </ul>"}, {"location": "app/#fast-build-system-pnpm-traefik-uv", "title": "🚀 Fast Build System (pnpm + Traefik + UV)", "text": "<p>This project uses a modern, high-performance build system:</p> <ul> <li>pnpm: For efficient package management with disk space optimization and faster builds</li> <li>Traefik: For efficient reverse proxy and routing</li> <li>UV: For optimized Python package management with dependency groups</li> </ul> <p>All build operations are handled through Docker and the Makefile for consistency.</p>"}, {"location": "app/#docker-based-development", "title": "🐳 Docker-based Development", "text": "<p>The easiest way to get started is using our optimized Docker Compose setup, which configures all services including the frontend, backend, and database.</p>"}, {"location": "app/#starting-the-environment", "title": "Starting the Environment", "text": "<pre><code># Using Makefile (recommended)\nmake up\n\n# Or directly with Docker Compose\ndocker compose up -d\n</code></pre>"}, {"location": "app/#accessing-services", "title": "Accessing Services", "text": "<ul> <li>Frontend: http://dashboard.localhost</li> <li>Backend API: http://api.localhost</li> <li>API Documentation: http://api.localhost/docs</li> <li>API ReDoc: http://api.localhost/redoc</li> <li>API OpenAPI Schema: http://api.localhost/openapi.json</li> <li>Traefik Dashboard: http://localhost:8080</li> </ul>"}, {"location": "app/#default-login-credentials_1", "title": "De<PERSON>ult <PERSON>gin Credentials", "text": "<p>After initialization, you can log in with:</p> <ul> <li>Email: <EMAIL></li> <li>Password: The value of <code>FIRST_SUPERUSER_PASSWORD</code> in your <code>.env</code> file</li> </ul>"}, {"location": "app/#viewing-logs", "title": "Viewing Logs", "text": "<pre><code># All services\ndocker compose logs -f\n\n# Specific service\ndocker compose logs -f backend\n</code></pre>"}, {"location": "app/#rebuilding-services", "title": "Rebuilding Services", "text": "<pre><code># After code changes\ndocker compose up -d --build\n\n# Restart all services\nmake restart\n</code></pre>"}, {"location": "app/#development-workflow", "title": "💻 Development Workflow", "text": "<p>All development must be done using the Makefile commands for consistency across environments. The Makefile abstracts away the complexity of individual tools and provides a standardized interface for all development tasks, ensuring that everyone follows the same processes regardless of their local setup.</p>"}, {"location": "app/#branching-strategy", "title": "🌿 Branching Strategy", "text": "<p>This project follows a structured branching strategy to ensure code quality and streamline the development process:</p> <ol> <li>Main Branch (<code>main</code>)</li> <li>Production-ready code only</li> <li>Protected from direct pushes</li> <li>Changes only accepted through PRs from the <code>dev</code> branch</li> <li> <p>Triggers production builds and deployments</p> </li> <li> <p>Development Branch (<code>dev</code>)</p> </li> <li>Integration branch for features and fixes</li> <li>Protected from direct pushes</li> <li>Changes only accepted through PRs from feature/fix branches</li> <li> <p>Triggers staging deployments for testing</p> </li> <li> <p>Feature Branches (<code>feat/*</code>)</p> </li> <li>Created for new features or enhancements</li> <li>Branched from <code>dev</code></li> <li>First push automatically opens a PR to <code>dev</code></li> <li> <p>Requires passing all tests and code reviews</p> </li> <li> <p>Fix Branches (<code>fix/*</code>)</p> </li> <li>Created for bug fixes</li> <li>Branched from <code>dev</code></li> <li>First push automatically opens a PR to <code>dev</code></li> <li> <p>Can be marked for auto-merge by adding <code>automerge</code> suffix</p> </li> <li> <p>Workflow Automation</p> </li> <li>When a PR to <code>dev</code> is merged, a new PR to <code>main</code> is automatically created</li> <li>All branches are automatically deleted after successful merge</li> </ol> <p>Creating Branches:</p> <p>Always use the Makefile commands to create branches to ensure proper naming and setup:</p> <pre><code># Create a feature branch\nmake branch-create type=feat name=your-feature-name\n\n# Create a fix branch\nmake branch-create type=fix name=your-fix-name\n\n# Create a fix branch with auto-merge enabled\nmake branch-create type=fix name=your-fix-name automerge=true\n</code></pre>"}, {"location": "app/#using-pnpm-and-uv-for-faster-builds", "title": "Using pnpm and UV for Faster Builds", "text": "<p>This project uses pnpm for frontend package management and UV for Python package management, significantly improving build times and reducing disk space usage:</p> <pre><code># Using Makefile (recommended)\nmake up  # Starts all services with pnpm and UV\n\n# Run pnpm commands through Makefile\nmake build  # Builds all workspaces (ensures containers are running)\nmake lint   # Runs linting across all workspaces\n\n# Run backend-specific tasks through Makefile\nmake test-backend  # Run backend tests\nmake backend-lint  # Run backend linting\n\n# Test login functionality\nmake check-login  # Verify API login works correctly\n</code></pre> <p>pnpm uses a content-addressable store for packages, making installations faster and more efficient. The node_modules are linked rather than copied, saving significant disk space. UV provides similar benefits for Python packages with its efficient dependency resolution and caching.</p>"}, {"location": "app/#development-workflow_1", "title": "🔄 Development Workflow", "text": ""}, {"location": "app/#branch-strategy", "title": "Branch Strategy", "text": "<ol> <li> <p>🌱 Feature Branches (<code>feat/* || fix/*</code>)</p> </li> <li> <p>Create for new features or bug fixes</p> </li> <li>Must pass pre-commit hooks before pushing</li> <li>On push triggers:<ul> <li>Style checks (ruff, eslint, prettier)</li> <li>Security checks (bandit, npm audit)</li> <li>Linting &amp; formatting</li> <li>Unit tests</li> </ul> </li> <li> <p>Requires PR review to merge to <code>dev</code></p> </li> <li> <p>🔨 Development Branch (<code>dev</code>)</p> </li> <li> <p>Integration branch for feature development</p> </li> <li>On push triggers:<ul> <li>Minimal test suite (unit, linting, security)</li> <li>Automatic staging deployment</li> </ul> </li> <li> <p>PR to <code>main</code> triggers:</p> <ul> <li>Full test suite (integration, e2e, API)</li> <li>Security scans</li> <li>Performance tests</li> <li>Documentation updates</li> <li>Changelog generation</li> </ul> </li> <li> <p>🚀 Main Branch (<code>main</code>)</p> </li> <li>Production-ready code</li> <li>Protected branch requiring PR approval</li> <li>On push/PR merge:<ul> <li>Complete test suite</li> <li>Security scans</li> <li>Dependency checks</li> </ul> </li> <li>Release tags trigger production deployment</li> </ol>"}, {"location": "app/#creating-a-feature", "title": "Creating a Feature", "text": "<pre><code>git checkout dev\ngit pull\ngit checkout -b feat/your-feature-name\n# Make changes\ngit commit -m \"feat: your feature description\"\n# Create PR to dev branch\n</code></pre>"}, {"location": "app/#testing-workflows-locally", "title": "Testing Workflows Locally", "text": "<p>You can test GitHub Actions workflows locally using the provided script:</p> <pre><code># Interactive mode - guides you through workflow selection\nnode scripts/test-workflow-selector.js\n\n# Test all workflows at once\nnode scripts/test-workflow-selector.js --all\n</code></pre> <p>In interactive mode, the script will guide you through selecting the workflow category, specific workflow file, and event type to test. Using the <code>--all</code> flag will test all workflows in all categories.</p> <p>Prerequisites: Before running workflow tests, you need to build the custom Docker image used for testing:</p> <pre><code># Build the workflow test Docker image\ndocker build -t local/workflow-test:latest -f .github/utils/Dockerfile.workflow-test .\n</code></pre>"}, {"location": "app/#cicd-pipeline", "title": "🔄 CI/CD Pipeline", "text": "<p>Our CI/CD pipeline uses GitHub Actions for automation and GitHub Container Registry for image management. The actual deployment is handled by a separate infrastructure repository:</p> <pre><code>graph LR\n    A[Push to feat/* or fix/*] --&gt; B{Branch Checks}\n    B --&gt;|Pass| C{fix/*-automerge?}\n    C --&gt;|No| D[Auto-Create PR]\n    C --&gt;|Yes| E[Auto-Merge to main]\n    D --&gt; F{PR Checks}\n    F --&gt;|Pass| E\n    E --&gt; G{Main Branch Checks}\n    G --&gt;|Pass| H[Create Release]\n    H --&gt; I[Push to GHCR]\n    style I fill:#2496ED,stroke:#fff,stroke-width:2px</code></pre>"}, {"location": "app/#github-container-registry-ghcr-configuration", "title": "GitHub Container Registry (GHCR) Configuration", "text": "<p>We use GitHub Container Registry to store and manage our Docker images:</p> <ul> <li>Image Repository: <code>ghcr.io/datascientest-fastapi-project-group-25/fastapi-project-app</code></li> <li>Tagging Strategy:</li> <li>Feature branches: <code>ghcr.io/datascientest-fastapi-project-group-25/fastapi-project-app:feat-branch-name</code></li> <li>Fix branches: <code>ghcr.io/datascientest-fastapi-project-group-25/fastapi-project-app:fix-branch-name</code></li> <li>Main branch: <code>ghcr.io/datascientest-fastapi-project-group-25/fastapi-project-app:latest</code></li> <li>Versioned releases: <code>ghcr.io/datascientest-fastapi-project-group-25/fastapi-project-app:v1.2.3</code></li> </ul>"}, {"location": "app/#authentication", "title": "Authentication", "text": "<p>The GitHub Actions workflows automatically authenticate with GHCR using the built-in <code>GITHUB_TOKEN</code> secret. For local development, you can authenticate using:</p> <pre><code># Login to GHCR\necho $GITHUB_TOKEN | docker login ghcr.io -u USERNAME --password-stdin\n\n# Pull an image\ndocker pull ghcr.io/datascientest-fastapi-project-group-25/fastapi-project-app:latest\n</code></pre>"}, {"location": "app/#documentation", "title": "📚 Documentation", "text": "<p>All project documentation is organized in the <code>docs/</code> directory for better maintainability:</p> <ul> <li>Development Guide - Setting up and running the application locally</li> <li>Deployment Guide - Deploying using GitHub Actions and GitHub Container Registry</li> <li>GitHub Actions Workflows - Overview and best practices for CI/CD workflows</li> <li>Git Hooks - Documentation for the pre-commit git hooks setup</li> <li>Release Notes - Comprehensive changelog of all project changes</li> </ul> <p>Component-specific documentation can be found in the respective directories:</p> <ul> <li>Backend Documentation</li> <li>Frontend Documentation</li> </ul> <p>For a complete overview of all documentation, see the Documentation Index.</p> <ol> <li> <p>Continuous Integration</p> </li> <li> <p>Automated testing</p> </li> <li>Code quality checks</li> <li>Security scanning</li> <li> <p>Performance testing</p> </li> <li> <p>Continuous Deployment</p> </li> <li>Staging environment (dev branch)</li> <li>Production environment (main branch releases)</li> <li>Deployment to target environments</li> <li>Docker image management in GitHub Container Registry (GHCR)</li> </ol>"}, {"location": "app/#environment-configuration", "title": "🔐 Environment Configuration", "text": "<p>The application uses environment variables for configuration. A sample <code>.env.example</code> file is provided as a template.</p>"}, {"location": "app/#important-environment-variables", "title": "Important Environment Variables", "text": "Variable Purpose Example <code>DOMAIN</code> Base domain for the application <code>localhost</code> <code>SECRET_KEY</code> Used for JWT token generation <code>your-secret-key</code> <code>BACKEND_CORS_ORIGINS</code> Configures CORS for the API <code>[\"http://localhost\"]</code> <code>POSTGRES_USER</code> Database username <code>postgres</code> <code>POSTGRES_PASSWORD</code> Database password <code>postgres</code> <code>POSTGRES_DB</code> Database name <code>app</code>"}, {"location": "app/#subdomain-based-routing", "title": "Subdomain-based Routing", "text": "<p>For local development, the application uses subdomain-based routing:</p> <ul> <li><code>api.localhost</code> - Backend API</li> <li><code>dashboard.localhost</code> - Frontend dashboard</li> <li><code>adminer.localhost</code> - Database administration</li> </ul> <p>To enable this on your local machine, add these entries to your hosts file:</p> <pre><code>127.0.0.1 api.localhost\n127.0.0.1 dashboard.localhost\n127.0.0.1 adminer.localhost\n</code></pre>"}, {"location": "app/#testing", "title": "🧪 Testing", "text": "<p>All testing should be performed using the Makefile commands to ensure consistent test environments and configurations. The Makefile provides a unified interface for running all types of tests, from unit tests to GitHub Actions workflow tests.</p>"}, {"location": "app/#running-tests-with-makefile-recommended", "title": "Running Tests with <PERSON><PERSON><PERSON> (Recommended)", "text": "<pre><code># Run all tests\nmake test\n\n# Run backend tests only\nmake test-backend\n\n# Run frontend tests only\nmake test-frontend\n\n# Run end-to-end tests\nmake test-e2e\n\n# Test GitHub Actions workflows locally\nmake act-test-main         # Test main-branch.yml workflow\nmake act-test-protection   # Test branch-protection.yml workflow\nmake act-test-all          # Test all workflows\nmake act-test-dry-run      # Dry run of workflows (no execution)\n</code></pre>"}, {"location": "app/#manual-testing-not-recommended", "title": "Manual Testing (Not Recommended)", "text": "<p>If you must run tests manually (not recommended):</p> <pre><code># Backend Tests\ncd backend\nsource .venv/bin/activate\npytest\n\n# Frontend Tests\ncd frontend\nnpm test\n\n# End-to-End Tests\ncd frontend\nnpm run test:e2e\n</code></pre>"}, {"location": "app/#troubleshooting", "title": "🔍 Troubleshooting", "text": ""}, {"location": "app/#common-issues", "title": "Common Issues", "text": "<ol> <li> <p>Docker Compose Network Issues</p> </li> <li> <p>Restart Docker: <code>docker compose down &amp;&amp; docker compose up -d</code></p> </li> <li> <p>Database Connection Failures</p> </li> <li> <p>Check database credentials in <code>.env</code></p> </li> <li> <p>Ensure PostgreSQL service is running: <code>docker compose ps</code></p> </li> <li> <p>Frontend API Connection Issues</p> </li> <li> <p>Verify CORS settings in <code>.env</code></p> </li> <li> <p>Check API URL configuration in frontend</p> </li> <li> <p>Login Issues</p> </li> <li> <p>If you can't log in, ensure the database is properly initialized: <code>make init-db</code></p> </li> <li>Default login credentials are:<ul> <li>Email: <EMAIL></li> <li>Password: Check your <code>.env</code> file for FIRST_SUPERUSER_PASSWORD</li> </ul> </li> <li>If login still fails, check the backend logs: <code>docker compose logs backend</code></li> <li> <p>For a complete database reset: <code>docker compose down -v &amp;&amp; make up &amp;&amp; make init-db</code></p> </li> <li> <p>Security Best Practices:</p> </li> <li>Never commit <code>.env</code> files to version control</li> <li>Use strong, unique passwords for all credentials</li> <li>Rotate secrets regularly in production environments</li> <li>Use different credentials for development, staging, and production</li> </ol>"}, {"location": "app/#subdomain-based-routing_1", "title": "Subdomain-Based Routing", "text": "<p>The application uses a subdomain-based routing approach for different services:</p> <ol> <li> <p>Local Development:</p> </li> <li> <p>API: http://api.localhost</p> </li> <li>Frontend: http://dashboard.localhost</li> <li>API Docs: http://api.localhost/docs</li> <li>API ReDoc: http://api.localhost/redoc</li> <li> <p>Adminer: http://db.localhost</p> </li> <li> <p>Configuration:</p> </li> <li> <p>The routing is handled by Traefik reverse proxy</p> </li> <li>Local development uses Traefik with appropriate hosts file entries</li> <li> <p>CORS is configured in Traefik to allow cross-subdomain communication</p> </li> <li> <p>Startup Information:</p> </li> </ol> <p>When you run <code>docker compose up</code>, you'll see:</p> <ul> <li>Application URLs for all services</li> <li>Default login credentials</li> <li>Database initialization status</li> <li>Health status of all components</li> </ul> <p>If you want to run the application in detached mode, use <code>docker compose up -d</code>.</p> <ul> <li> <p>than you can see the startup information in the logs <code>docker compose logs app-status</code></p> </li> <li> <p>Adding a Host Entry (Local Development):    <pre><code># Add to /etc/hosts\n127.0.0.1 api.localhost dashboard.localhost db.localhost\n</code></pre></p> </li> </ul>"}, {"location": "app/#contributing", "title": "Contributing", "text": "<ol> <li>Fork the repository</li> <li>Create your feature branch (<code>git checkout -b feat/amazing-feature</code>)</li> <li>Commit your changes (<code>git commit -m 'feat: add amazing feature'</code>)</li> <li>Push to the branch (<code>git push origin feat/amazing-feature</code>)</li> <li>Open a Pull Request to the <code>dev</code> branch</li> </ol>"}, {"location": "infra/", "title": "FastAPI Project Infrastructure", "text": "<p>Infrastructure as Code (IaC) repository for managing the FastAPI project infrastructure using Terraform.</p>"}, {"location": "infra/#project-overview", "title": "📋 Project Overview", "text": "<p>This repository contains the infrastructure code for the FastAPI project, organized into two main components:</p> Component Description Bootstrap Sets up foundational AWS resources (S3, DynamoDB, IAM) Terraform Manages the main application infrastructure"}, {"location": "infra/#architecture", "title": "🏗️ Architecture", "text": ""}, {"location": "infra/#directory-structure", "title": "📁 Directory Structure", "text": "Directory Description <code>bootstrap/</code> Infrastructure bootstrap code <code>bootstrap/environments/</code> Environment-specific configurations <code>bootstrap/modules/</code> Reusable Terraform modules <code>bootstrap/scripts/</code> Utility scripts for environment setup"}, {"location": "infra/#quick-start", "title": "🚀 Quick Start", "text": ""}, {"location": "infra/#prerequisites", "title": "Prerequisites", "text": "<ol> <li>AWS CLI: Installed and configured with appropriate credentials</li> <li>Terraform: Version 1.0.0 or later</li> <li>Make: For running automation commands</li> <li>Docker: Required for running LocalStack and the dockerized environments</li> </ol>"}, {"location": "infra/#environment-setup", "title": "Environment Setup", "text": "<ol> <li> <p>Clone the repository <pre><code>git clone https://github.com/yourusername/fastapi-project-infra.git\ncd fastapi-project-infra\n</code></pre></p> </li> <li> <p>Set up environment variables <pre><code>cp bootstrap/.env.base.example bootstrap/.env.base\n# Edit the file with your AWS credentials\n</code></pre></p> </li> </ol>"}, {"location": "infra/#localstack-development", "title": "LocalStack Development", "text": "<pre><code># Start LocalStack\nmake -C bootstrap start-localstack\n\n# Initialize Terraform\nmake -C bootstrap local-init\n\n# Plan and apply changes\nmake -C bootstrap local-plan\nmake -C bootstrap local-apply\n\n# Run a bootstrap dry run (create, test, destroy resources)\nmake -C bootstrap localstack-bootstrap-dryrun\n\n# Clean up when done\nmake -C bootstrap local-destroy\nmake -C bootstrap stop-localstack\n</code></pre>"}, {"location": "infra/#aws-deployment", "title": "AWS Deployment", "text": "<pre><code># Prepare AWS environment (package Lambda functions)\nmake -C bootstrap aws-prepare\n\n# Set up Terraform state resources\nmake -C bootstrap aws-setup-state\n\n# Initialize Terraform\nmake -C bootstrap aws-init\n\n# Plan and apply changes\nmake -C bootstrap aws-plan\nmake -C bootstrap aws-apply\n\n# Run a bootstrap dry run (create, test, destroy resources)\nmake -C bootstrap aws-bootstrap-dryrun\n</code></pre>"}, {"location": "infra/#dockerized-environments", "title": "Dockerized Environments", "text": "<p>Both bootstrap environments (AWS and LocalStack) have been dockerized to ensure they can run on any system.</p> <pre><code># Build Docker images\nmake -C bootstrap docker-build\n\n# Start AWS environment in Docker\nmake -C bootstrap docker-aws\n\n# Start LocalStack environment in Docker\nmake -C bootstrap docker-localstack\n\n# Run bootstrap dry run in AWS using Docker\nmake -C bootstrap docker-aws-bootstrap-dryrun\n\n# Run bootstrap dry run in LocalStack using Docker\nmake -C bootstrap docker-localstack-bootstrap-dryrun\n\n# Test both environments\nmake -C bootstrap docker-test\n\n# Clean up Docker resources\nmake -C bootstrap docker-clean\n</code></pre> <p>For more information, see the Bootstrap README.</p>"}, {"location": "infra/#make-commands", "title": "🛠️ Make Commands", "text": ""}, {"location": "infra/#root-makefile-commands", "title": "Root Makefile Commands", "text": "Command Description <code>make ENV=aws tf_plan</code> Run Terraform plan for AWS environment <code>make tf_plan</code> Run Terraform plan for LocalStack environment (default) <code>make ENV=test test</code> Run tests using test environment <code>make ENV=local-test act_mock</code> Run GitHub Actions locally with Act <code>make git_feature</code> Create a new feature branch <code>make git_fix</code> Create a new fix branch <code>make git_commit</code> Commit changes in logical groups <code>make git_push</code> Push current branch to remote <code>make git_merge_main</code> Merge current branch to main branch <code>make git_status</code> Show git status <code>make help</code> Show all available commands"}, {"location": "infra/#bootstrap-makefile-commands", "title": "Bootstrap Makefile Commands", "text": "Command Description <code>make -C bootstrap start-localstack</code> Start LocalStack container <code>make -C bootstrap local-init</code> Initialize Terraform for LocalStack <code>make -C bootstrap local-apply</code> Apply changes to LocalStack <code>make -C bootstrap aws-prepare</code> Package Lambda for AWS deployment <code>make -C bootstrap aws-bootstrap-dryrun</code> Run AWS bootstrap dry run <code>make -C bootstrap help</code> Show all bootstrap commands"}, {"location": "infra/#docker-based-bootstrap-commands", "title": "Docker-based Bootstrap Commands", "text": "Command Description <code>make -C bootstrap docker-build</code> Build Docker images for AWS and LocalStack <code>make -C bootstrap docker-aws</code> Start AWS environment in Docker <code>make -C bootstrap docker-localstack</code> Start LocalStack environment in Docker <code>make -C bootstrap docker-aws-setup-state</code> Set up Terraform state in AWS using Docker <code>make -C bootstrap docker-aws-bootstrap-dryrun</code> Run AWS bootstrap dry run in Docker <code>make -C bootstrap docker-localstack-bootstrap-dryrun</code> Run LocalStack bootstrap dry run in Docker <code>make -C bootstrap docker-test</code> Test both Docker environments <code>make -C bootstrap docker-clean</code> Clean up Docker resources"}, {"location": "infra/#aws-credentials", "title": "🔐 AWS Credentials", "text": ""}, {"location": "infra/#required-credentials", "title": "Required Credentials", "text": "Credential Description Secret Name AWS Account ID Your 12-digit AWS account number <code>AWS_ACCOUNT_ID</code> Access Key ID AWS access key for authentication <code>AWS_ACCESS_KEY_ID</code> Secret Access Key AWS secret key for authentication <code>AWS_SECRET_ACCESS_KEY</code>"}, {"location": "infra/#setup-process", "title": "Setup Process", "text": "<ol> <li>Navigate to repository settings</li> <li>Go to your GitHub repository</li> <li> <p>Click \"Settings\" → \"Secrets and variables\" → \"Actions\"</p> </li> <li> <p>Add the required secrets:    <pre><code>AWS_ACCOUNT_ID         # Your 12-digit AWS account ID\nAWS_ACCESS_KEY_ID      # Your AWS access key\nAWS_SECRET_ACCESS_KEY  # Your AWS secret key\n</code></pre></p> </li> </ol>"}, {"location": "infra/#usage-in-workflows", "title": "Usage in Workflows", "text": "<p>The GitHub Actions workflows automatically use these secrets:</p> <pre><code>env:\n  AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}\n  AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}\n  AWS_ACCOUNT_ID: ${{ secrets.AWS_ACCOUNT_ID }}\n  AWS_DEFAULT_REGION: eu-west-2\n</code></pre>"}, {"location": "infra/#security-best-practices", "title": "Security Best Practices", "text": "Practice Description Use Secrets Never commit credentials to the repository Limit Permissions Use IAM roles with minimal required access Rotate Keys Regularly change access keys Monitor Activity Watch for unusual AWS account activity"}, {"location": "infra/#environment-variables", "title": "🌍 Environment Variables", "text": ""}, {"location": "infra/#environment-structure", "title": "Environment Structure", "text": ""}, {"location": "infra/#file-structure", "title": "File Structure", "text": "Location File Purpose Root <code>.env.base</code> Common settings for all environments Root <code>.env.&lt;environment&gt;</code> Environment-specific settings Bootstrap <code>bootstrap/.env.base</code> Common bootstrap settings Bootstrap <code>bootstrap/.env.&lt;environment&gt;</code> Bootstrap-specific settings Environments <code>bootstrap/environments/aws/.env.aws</code> AWS-specific variables Environments <code>bootstrap/environments/localstack/.env.local</code> LocalStack-specific variables Tests <code>tests/.env.test</code> Test-specific variables Tests <code>tests/.env.local-test</code> Local test variables for GitHub Actions"}, {"location": "infra/#loading-order", "title": "Loading Order", "text": "<p>Variables are loaded in the following order, with later files overriding earlier ones:</p> <ol> <li><code>.env.base</code> → 2. <code>.env.&lt;environment&gt;</code> → 3. <code>bootstrap/.env.base</code> → 4. <code>bootstrap/.env.&lt;environment&gt;</code></li> </ol>"}, {"location": "infra/#example-variables", "title": "Example Variables", "text": "<pre><code># Common Variables (.env.base)\nAWS_ACCESS_KEY_ID=your-aws-access-key\nAWS_SECRET_ACCESS_KEY=your-aws-secret-key\nAWS_ACCOUNT_ID=your-aws-account-id\nPROJECT_NAME=fastapi-project\n\n# Environment Variables (.env.&lt;environment&gt;)\nAWS_DEFAULT_REGION=eu-west-2\nENVIRONMENT=dev\n\n# AWS-Specific Variables\nAWS_BOOTSTRAP_ROLE_NAME=terraform-bootstrap-role\nAWS_BOOTSTRAP_POLICY_NAME=terraform-bootstrap-policy\n</code></pre>"}, {"location": "infra/#development-workflow", "title": "🔄 Development Workflow", "text": "<ol> <li>Bootstrap infrastructure provides foundational resources</li> <li>Deploy main infrastructure using bootstrapped resources</li> <li>Test changes locally using LocalStack</li> <li>Contribute by creating pull requests</li> </ol>"}, {"location": "infra/#git-workflow", "title": "🌿 Git Workflow", "text": "<p>This project follows a trunk-based development model to maintain code quality and facilitate collaboration.</p>"}, {"location": "infra/#branch-structure", "title": "Branch Structure", "text": "<ul> <li><code>main</code>: Production branch (protected)</li> <li><code>feat/*</code>: Feature branches</li> <li><code>fix/*</code>: Bug fix branches</li> </ul>"}, {"location": "infra/#environment-structure_1", "title": "Environment Structure", "text": "<p>Instead of using separate branches for different environments, we use folder-based environments:</p> <ul> <li><code>environments/stg/</code>: Configuration for the staging environment.</li> <li><code>environments/prod/</code>: Configuration for the production environment.</li> </ul>"}, {"location": "infra/#git-commands", "title": "Git Commands", "text": "Command Description <code>make git_feature</code> Create a new feature branch <code>make git_fix</code> Create a new fix branch <code>make git_commit</code> Commit changes in logical groups <code>make git_push</code> Push current branch to remote <code>make git_merge_main</code> Merge current branch to main branch <code>make git_status</code> Show git status <p>For detailed information about the Git workflow, see BRANCHING.md.</p> <p>An example script demonstrating the Git workflow is available in examples/git-workflow-example.sh.</p>"}, {"location": "infra/#license", "title": "📄 License", "text": "<p>See LICENSE file.</p>"}, {"location": "release/", "title": "FastAPI Project Release", "text": "<p>This repository contains the Kubernetes manifests, Helm charts, and Argo CD configurations for deploying the FastAPI application.</p>"}, {"location": "release/#documentation", "title": "Documentation", "text": "<p>Detailed documentation is available in the <code>docs/</code> directory:</p> <ul> <li>Release Strategy - Comprehensive guide to the release strategy</li> <li>Quick Start Guide - Quick start guide for new team members</li> <li>Troubleshooting - Solutions to common issues</li> </ul>"}, {"location": "release/#repository-structure", "title": "Repository Structure", "text": "<pre><code>.\n├── charts/                    # Helm charts\n│   └── fastapi/              # FastAPI application Helm chart\n│       ├── Chart.yaml        # Chart metadata\n│       ├── values.yaml       # Default values\n│       └── templates/        # Helm templates\n│           ├── _helpers.tpl\n│           ├── backend-deployment.yaml\n│           ├── configmap.yaml\n│           ├── frontend-deployment.yaml\n│           ├── ingress.yaml\n│           ├── postgres-statefulset.yaml\n│           ├── services.yaml\n│           └── db-init-script-configmap.yaml  # Database initialization and migrations\n├── config/                   # Environment-specific configurations\n│   ├── argocd/              # Argo CD Application manifests\n│   │   ├── staging.yaml     # Staging environment\n│   │   └── production.yaml  # Production environment\n│   └── helm/                # Environment-specific Helm values\n│       ├── values.yaml      # Default values (development)\n│       ├── staging.yaml     # Staging environment\n│       └── production.yaml  # Production environment\n├── scripts/                  # Deployment and maintenance scripts\n│   ├── deploy-dev.sh        # Development deployment script\n│   ├── deploy-prod.sh       # Production deployment script\n│   ├── cleanup.sh           # Environment cleanup script\n│   ├── setup-argocd.sh      # ArgoCD setup script\n│   └── setup-argocd-integration.sh # ArgoCD CI/CD integration script\n└── .github/                 # GitHub Actions workflows\n    └── workflows/\n        ├── helm-deploy.yml  # Deployment workflow\n        ├── helm-test.yml    # Helm chart testing workflow\n        ├── pr-automation.yml # PR automation workflow with branch deletion on merge\n        ├── helm-argocd-test.yml # ArgoCD configuration testing workflow\n        └── argocd-integration.yml # ArgoCD integration workflow\n</code></pre>"}, {"location": "release/#prerequisites", "title": "Prerequisites", "text": "<p>The project uses several tools that will be automatically checked and installed as needed. To begin:</p> <pre><code>make init\n</code></pre> <p>This command will: 1. Detect your operating system (macOS, Linux, or Windows) 2. Install Bun (JavaScript/TypeScript runtime) if not present 3. Check for and help you install other required tools:    - Git (Version control)    - Docker (Container runtime)    - <PERSON><PERSON>ct<PERSON> (Kubernetes CLI)    - k3d (Local Kubernetes)    - <PERSON><PERSON> (Kubernetes package manager)</p> <p>The initialization process is platform-aware and will provide appropriate installation instructions for your system.</p> <p>For macOS users with Homebrew, you can install all required tools with: <pre><code>brew install git docker kubectl k3d helm\n</code></pre></p> <p>You'll also need: - Kubernetes cluster - Argo CD installed - GitHub Container Registry access</p>"}, {"location": "release/#release-strategy", "title": "Release Strategy", "text": "<p>This project follows a streamlined release strategy with feature/fix branches that merge directly into the main branch:</p> <ol> <li>Development Workflow:</li> <li>Create feature/fix branches from main (<code>feat/*</code> or <code>fix/*</code>)</li> <li>Push changes to GitHub to automatically create a PR</li> <li>PR triggers tests and validation workflows</li> <li> <p>After review and approval, merge to main</p> </li> <li> <p>Deployment Process:</p> </li> <li>Main branch changes trigger deployment to staging</li> <li>After validation in staging, promote to production</li> <li> <p>ArgoCD manages the deployment process</p> </li> <li> <p>Environments:</p> </li> <li>Staging: Pre-production environment for validation</li> <li>Production: Live environment</li> </ol>"}, {"location": "release/#environment-overview", "title": "Environment Overview", "text": ""}, {"location": "release/#development", "title": "Development", "text": "<ul> <li>Branch: <code>main</code></li> <li>Values: <code>config/helm/values.yaml</code></li> <li>Features:</li> <li>Debug mode enabled</li> <li>Minimal resources</li> <li>Local development optimized</li> </ul>"}, {"location": "release/#staging", "title": "Staging", "text": "<ul> <li>Branch: <code>stg</code></li> <li>Values: <code>config/helm/staging.yaml</code></li> <li>Features:</li> <li>Debugging enabled</li> <li>Moderate resource limits</li> <li>Automated deployments</li> <li>Single replica per service</li> </ul>"}, {"location": "release/#production", "title": "Production", "text": "<ul> <li>Branch: <code>main</code></li> <li>Values: <code>config/helm/production.yaml</code></li> <li>Features:</li> <li>Debugging disabled</li> <li>High resource limits</li> <li>Multiple replicas</li> <li>Autoscaling enabled</li> <li>Enhanced security</li> <li>TLS enabled</li> </ul>"}, {"location": "release/#deployment-methods", "title": "Deployment Methods", "text": ""}, {"location": "release/#using-scripts", "title": "Using Scripts", "text": "<p>The repository includes several utility scripts to manage deployments:</p> <pre><code># Deploy to development environment\n./scripts/deploy-dev.sh\n\n# Deploy to production environment\n./scripts/deploy-prod.sh\n\n# Clean up environments\n./scripts/cleanup.sh dev    # Clean development environment\n./scripts/cleanup.sh prod   # Clean production environment\n./scripts/cleanup.sh all    # Clean all environments\n</code></pre>"}, {"location": "release/#using-docker-platform-agnostic-setup", "title": "Using Docker (Platform-Agnostic Setup)", "text": "<p>For a consistent setup experience across different platforms (Linux, macOS, Windows), you can use the Docker-based setup:</p> <pre><code># Set up a local k3d cluster using Docker\nmake setup-k3d-docker\n</code></pre> <p>This method uses a Docker container that includes all the necessary tools (k3d, kubectl, Helm) and runs the setup script inside the container. This approach ensures that the setup process is consistent regardless of the host operating system.</p> <p>Requirements: - Docker - Docker Compose</p> <p>The Docker-based setup automatically: 1. Builds a container with all required tools 2. Sets up a local k3d cluster 3. Installs ArgoCD 4. Configures the necessary components</p>"}, {"location": "release/#using-github-actions", "title": "Using GitHub Actions", "text": "<p>The project uses GitHub Actions for CI/CD with the following workflows:</p> <ol> <li>PR Automation (<code>pr-automation.yml</code>)</li> <li>Triggers on pushes to feature/ and fix/ branches</li> <li>Automatically creates a PR if one doesn't exist</li> <li>Adds appropriate labels and descriptions</li> <li> <p>Deletes branches automatically after PR is merged</p> </li> <li> <p>ArgoCD Configuration Tests (<code>helm-argocd-test.yml</code>)</p> </li> <li>Triggers on PR creation and updates affecting ArgoCD configurations</li> <li>Validates ArgoCD application manifests</li> <li>Prepares deployment manifests for testing</li> <li> <p>Focuses specifically on ArgoCD-related configurations</p> </li> <li> <p>Helm Chart Test (<code>helm-test.yml</code>)</p> </li> <li>Triggers on pull requests and pushes to main</li> <li>Validates Helm charts across all environments</li> <li>Runs comprehensive chart-testing</li> <li> <p>Tests chart installation in a Kind cluster</p> </li> <li> <p>Helm Chart Deploy (<code>helm-deploy.yml</code>)</p> </li> <li>Deploys to development, staging, and production environments</li> <li>Supports manual triggering with environment selection</li> <li>Includes validation and verification steps</li> <li> <p>Manages environment-specific configurations</p> </li> <li> <p>ArgoCD Integration (<code>argocd-integration.yml</code>)</p> </li> <li>Manually triggered workflow for ArgoCD integration</li> <li>Configures ArgoCD applications for different environments</li> <li>Manages deployment synchronization</li> <li>Provides deployment status and URLs</li> </ol>"}, {"location": "release/#deployment-process", "title": "Deployment Process", "text": "<ol> <li>Images are built and pushed to GitHub Container Registry (ghcr.io/datascientest-fastapi-project-group-25)</li> <li>CI pipeline updates image tags in the appropriate values file</li> <li>Argo CD detects changes and syncs the application</li> </ol>"}, {"location": "release/#initial-setup", "title": "Initial Setup", "text": "<ol> <li> <p>Install Argo CD:    <pre><code>kubectl create namespace argocd\nkubectl apply -n argocd -f https://raw.githubusercontent.com/argoproj/argo-cd/stable/manifests/install.yaml\n</code></pre></p> </li> <li> <p>Configure GitHub Container Registry credentials:    <pre><code>kubectl create secret docker-registry ghcr-secret \\\n  --docker-server=ghcr.io \\\n  --docker-username=&lt;github-username&gt; \\\n  --docker-password=&lt;github-pat&gt; \\\n  --namespace=fastapi-helm\n</code></pre></p> </li> <li> <p>Set up ArgoCD API key for CI/CD integration and store it as a GitHub secret:    <pre><code># Run the setup script to configure ArgoCD, generate an API key, and store it as a GitHub secret\n./scripts/setup-argocd-github.sh\n</code></pre></p> </li> <li> <p>Apply Argo CD applications:    <pre><code># For staging\nkubectl apply -f config/argocd/staging.yaml\n\n# For production\nkubectl apply -f config/argocd/production.yaml\n</code></pre></p> </li> </ol>"}, {"location": "release/#argocd-integration", "title": "ArgoCD Integration", "text": "<p>The repository includes scripts and workflows for ArgoCD integration:</p> <ol> <li>Manual Setup:</li> <li>Use <code>./scripts/setup-argocd-github.sh</code> to install and configure ArgoCD</li> <li>Generate an API key for CI/CD integration</li> <li> <p>Automatically store the API key and server URL as GitHub secrets (<code>ARGOCD_AUTH_TOKEN</code> and <code>ARGOCD_SERVER</code>)</p> </li> <li> <p>CI/CD Integration:</p> </li> <li>The <code>argocd-integration.yml</code> workflow configures ArgoCD applications</li> <li>Main branch changes trigger deployment to staging</li> <li> <p>After validation, changes can be promoted to production</p> </li> <li> <p>PR Testing:</p> </li> <li>When a PR is created, the <code>helm-argocd-test.yml</code> workflow validates Helm charts and ArgoCD configurations</li> <li>The workflow prepares deployment manifests for testing</li> <li>These manifests can be used for manual testing or review</li> </ol>"}, {"location": "release/#configuration", "title": "Configuration", "text": ""}, {"location": "release/#image-tags", "title": "Image Tags", "text": "<ul> <li>Development: <code>dev-latest</code> or <code>dev-[commit-sha]</code></li> <li>Staging: <code>staging-latest</code> or <code>staging-[commit-sha]</code></li> <li>Production: <code>production-latest</code> or <code>production-[commit-sha]</code></li> </ul>"}, {"location": "release/#resource-configurations", "title": "Resource Configurations", "text": ""}, {"location": "release/#development_1", "title": "Development", "text": "<pre><code>resources:\n  limits:\n    cpu: 200m\n    memory: 256Mi\n  requests:\n    cpu: 100m\n    memory: 128Mi\n</code></pre>"}, {"location": "release/#staging_1", "title": "Staging", "text": "<pre><code>resources:\n  limits:\n    cpu: 500m\n    memory: 512Mi\n  requests:\n    cpu: 200m\n    memory: 256Mi\n</code></pre>"}, {"location": "release/#production_1", "title": "Production", "text": "<pre><code>resources:\n  limits:\n    cpu: 1000m\n    memory: 1024Mi\n  requests:\n    cpu: 500m\n    memory: 512Mi\n</code></pre>"}, {"location": "release/#security-considerations", "title": "Security Considerations", "text": "<ul> <li>All secrets should be managed through AWS Secrets Manager</li> <li>Debug mode is disabled in production</li> <li>Network policies restrict pod communication</li> <li>TLS is enabled for production ingress</li> <li>Pods run as non-root users</li> <li>Resource limits are enforced</li> <li>HPA ensures proper scaling</li> </ul>"}, {"location": "release/#monitoring", "title": "Monitoring", "text": "<ul> <li>Kubernetes metrics</li> <li>Application health checks</li> <li>Resource utilization</li> <li>Autoscaling behavior</li> <li>Deployment status through Argo CD UI</li> </ul>"}, {"location": "release/#contributing", "title": "Contributing", "text": "<ol> <li>Create a new branch from the target environment branch</li> <li>Make changes to the appropriate values file</li> <li>Create a pull request</li> <li>After review and approval, changes will be deployed automatically</li> </ol>"}, {"location": "release/#support", "title": "Support", "text": "<p>For issues or questions, please contact DataScientest Group 25: - GitHub: datascientest-fastapi-project-group-25</p>"}, {"location": "release/#need-to-be-updated-in-test-docs", "title": "need to be updated in test-docs", "text": ""}, {"location": "release/#test-change-for-github-actions", "title": "Test change for GitHub Actions", "text": ""}, {"location": "release/#another-test-change", "title": "Another test change", "text": ""}]}