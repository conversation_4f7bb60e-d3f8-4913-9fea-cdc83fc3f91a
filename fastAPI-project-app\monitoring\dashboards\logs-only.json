{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": null, "links": [], "panels": [{"datasource": {"type": "loki", "uid": "loki"}, "gridPos": {"h": 24, "w": 24, "x": 0, "y": 0}, "id": 1, "options": {"dedupStrategy": "none", "enableLogDetails": true, "prettifyLogMessage": false, "showCommonLabels": false, "showLabels": false, "showTime": false, "sortOrder": "Descending", "wrapLogMessage": false}, "targets": [{"datasource": {"type": "loki", "uid": "loki"}, "expr": "{job=\"docker\"} |= \"fastapi-project-app-backend\"", "refId": "A"}], "title": "Backend Logs", "type": "logs"}], "refresh": "5s", "schemaVersion": 38, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Backend Logs", "uid": "backend-logs", "version": 0, "weekStart": ""}