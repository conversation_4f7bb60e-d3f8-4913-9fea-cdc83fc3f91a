{"$schema": "https://biomejs.dev/schemas/1.6.1/schema.json", "organizeImports": {"enabled": true}, "files": {"ignore": ["node_modules", "dist/**", "coverage/**"]}, "linter": {"enabled": true, "rules": {"recommended": true, "correctness": {"noUnusedVariables": "error"}, "suspicious": {"noConsoleLog": "warn"}, "style": {"useConst": "error", "useTemplate": "error"}}}, "formatter": {"enabled": true, "indentWidth": 2, "indentStyle": "space", "lineWidth": 100}, "javascript": {"formatter": {"quoteStyle": "double", "trailingComma": "es5", "semicolons": "always"}}}