entryPoints:
  web:
    address: ":80"
    http:
      redirections:
        entryPoint:
          to: websecure
          scheme: https
          permanent: true
  websecure:
    address: ":443"

providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    network: web
  file:
    directory: "/etc/traefik/dynamic"
    watch: true

certificatesResolvers:
  letsencrypt:
    acme:
      email: "${ACME_EMAIL:-<EMAIL>}"
      storage: "/etc/traefik/acme/acme.json"
      httpChallenge:
        entryPoint: web

api:
  dashboard: true
  insecure: false

log:
  level: "INFO"

accessLog: {}
