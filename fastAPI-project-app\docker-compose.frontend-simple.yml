services:
  frontend:
    build:
      context: .
      dockerfile: frontend/Dockerfile.simple
    ports:
      - "5173:5173"
    volumes:
      - ./:/app
      - /app/node_modules
      - /app/frontend/node_modules
    environment:
      - VITE_API_URL=http://api.localhost
      - NODE_ENV=development
      - HOST=0.0.0.0
    restart: unless-stopped

volumes:
  frontend-node-modules:
  frontend-frontend-node-modules:
