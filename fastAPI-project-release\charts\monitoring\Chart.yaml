apiVersion: v2
name: monitoring
description: A Helm chart for monitoring stack (Prometheus, Graf<PERSON>, Loki, Promtail)
type: application
version: 0.1.0
appVersion: "1.0.0"

maintainers:
  - name: FastAPI Project Team
    email: <EMAIL>

sources:
  - https://github.com/datascientest-fastAPI-project-group-25/fastAPI-project-release

keywords:
  - monitoring
  - prometheus
  - grafana
  - loki
  - promtail

dependencies:
  - name: prometheus
    version: 15.18.0
    repository: "https://prometheus-community.github.io/helm-charts"
    condition: prometheus.enabled
  - name: grafana
    version: 6.36.0
    repository: "https://grafana.github.io/helm-charts"
    condition: grafana.enabled
  - name: loki
    version: 4.0.0
    repository: "https://grafana.github.io/helm-charts"
    condition: loki.enabled
