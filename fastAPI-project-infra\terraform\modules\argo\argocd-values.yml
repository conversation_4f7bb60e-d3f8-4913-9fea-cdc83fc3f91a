server:
  service:
    type: LoadBalancer  # Use Ingress instead for production
  ingress:
    enabled: true

  extraArgs:
    - --insecure

configs:
  cm:
    application.instanceLabelKey: argocd.argoproj.io/instance
    kustomize.buildOptions: "--enable-helm"
    repositories: |
      - url: https://github.com/datascientest-fastAPI-project-group-25/fastAPI-project-release

  secret:
    # Optional: Set ArgoCD admin password (hashed)
    # Can use: htpasswd -nbBC 10 "" your-password | tr -d ':\n' | sed 's/$2y/$2a/'
    argocdServerAdminPassword: $2a$10$7QXs6m5YOvA3R2qY7gX5TeZhE6xLM4ZTzOt1smPvFHvDOjvhnNGmG

  params:
    server.insecure: true

rbac:
  policy.default: role:readonly
  policy.csv: |
    g, system:authenticated, role:admin
