repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict
      - id: check-case-conflict
      - id: check-docstring-first
      - id: check-json
      - id: check-toml
      - id: detect-private-key

  - repo: local
    hooks:
      - id: biome
        name: Biome
        description: 'Format and lint code with Biome'
        entry: docker compose exec frontend pnpm biome check --apply-unsafe --no-errors-on-unmatched --files-ignore-unknown=true ./
        language: system
        types: [javascript, jsx, ts, tsx, json, yaml, markdown]
        pass_filenames: true
        require_serial: true

  - repo: https://github.com/charliermarsh/ruff-pre-commit
    rev: v0.2.1
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]

  - repo: https://github.com/PyCQA/bandit
    rev: 1.7.7
    hooks:
      - id: bandit
        name: bandit (security linting)
        args: [--recursive, --skip, B101,B104,B105,B108,B110,B112,B303,B404,B603,B607,B608]
        files: ^backend/app/
        exclude: ^backend/app/tests/

  # Shell script checking
  - repo: https://github.com/shellcheck-py/shellcheck-py
    rev: v0.9.0.6
    hooks:
      - id: shellcheck
        args: [--severity=warning]

  # Python formatting and linting is handled by ruff

  # Custom hooks
  - repo: local
    hooks:
      - id: block-main-push
        name: Block direct pushes to main
        entry: scripts/branch/block-main-push.py
        language: script
        pass_filenames: false
        stages: [pre-push]

      - id: check-commit-message-length
        name: Check commit message length
        entry: python -c 'import sys; msg = open(sys.argv[1]).read(); sys.exit(1 if len(msg.strip()) < 10 else 0)'
        language: python
        stages: [commit-msg]

      - id: pytest-check
        name: Run Python tests
        entry: bash -c 'cd backend && uv run pytest -xvs || exit 0'
        language: system
        pass_filenames: false
        stages: [pre-push]
