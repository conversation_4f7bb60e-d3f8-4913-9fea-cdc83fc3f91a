# Release Logging

This directory contains the logging configuration for the release process.

## Overview

The logging setup for releases uses <PERSON> and <PERSON><PERSON><PERSON> to collect and store logs from the release process, which can then be visualized in Grafana.

## Components

- **Loki**: Collects and stores logs
- **Promtail**: Forwards logs to Loki
- **<PERSON>ana**: Visualizes logs

## Configuration

The logging configuration is defined in the following files:

- `loki-release-config.yml`: Loki configuration for release logging
- `promtail-release-config.yml`: Promtail configuration for release logging

## Setup

The logging setup is integrated with the monitoring setup. To set up logging:

1. Start the release monitoring and logging:
   ```bash
   make up-release-monitoring
   ```

2. Access the Grafana dashboard:
   - URL: http://grafana.localhost
   - Default credentials: admin/admin

## Log Levels

The release process uses the following log levels:

- **DEBUG**: Detailed information, typically of interest only when diagnosing problems
- **INFO**: Confirmation that things are working as expected
- **WARNING**: An indication that something unexpected happened, or may happen in the near future
- **ERROR**: Due to a more serious problem, the software has not been able to perform some function
- **CRITICAL**: A serious error, indicating that the program itself may be unable to continue running

## Log Format

Logs are formatted as JSON with the following fields:

- **timestamp**: The time the log was generated
- **level**: The log level
- **message**: The log message
- **component**: The component that generated the log
- **release_id**: The ID of the release that generated the log
- **environment**: The environment (staging, production) that the log is for

## Viewing Logs

Logs can be viewed in Grafana:

1. Go to the Explore view
2. Select Loki as the data source
3. Use LogQL to query logs

Example queries:

- `{job="release"}`: All logs from the release process
- `{job="release"} |= "ERROR"`: All ERROR logs from the release process
- `{job="release"} |= "environment=production"`: All logs for production releases
