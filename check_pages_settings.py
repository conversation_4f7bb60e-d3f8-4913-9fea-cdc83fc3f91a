import requests
import os
import json

# Replace with your GitHub token if you have one
# token = os.environ.get("GITHUB_TOKEN")
# headers = {"Authorization": f"token {token}"} if token else {}
headers = {}

# Repository information
owner = "datascientest-fastAPI-project-group-25"
repo = "fastAPI-project-docs"

# Get repository settings
url = f"https://api.github.com/repos/{owner}/{repo}/pages"
response = requests.get(url, headers=headers)

print(f"Status code: {response.status_code}")
if response.status_code == 200:
    print(json.dumps(response.json(), indent=2))
else:
    print(f"Error: {response.text}")
