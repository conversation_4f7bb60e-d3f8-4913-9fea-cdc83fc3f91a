# Global configuration
global:
  namespace: monitoring

# Prometheus configuration
prometheus:
  enabled: true
  server:
    service:
      type: ClusterIP
      port: 9090
    persistence:
      enabled: true
      storageClassName: "standard"
      size: "10Gi"
    resources:
      requests:
        memory: "400Mi"
        cpu: "500m"
      limits:
        memory: "800Mi"
        cpu: "1"
    retention: 15d
    scrapeInterval: 15s
    evaluationInterval: 15s

# Grafana configuration
grafana:
  enabled: true
  admin:
    existingSecret: grafana-secrets
    userKey: admin-user
    passwordKey: admin-password
  service:
    type: ClusterIP
    port: 3000
  persistence:
    enabled: true
    storageClassName: "standard"
    size: "10Gi"
  resources:
    requests:
      memory: "400Mi"
      cpu: "500m"
    limits:
      memory: "800Mi"
      cpu: "1"
  sidecar:
    datasources:
      enabled: true
      label: grafana_datasource
    dashboards:
      enabled: true
      label: grafana_dashboard

# Loki configuration
loki:
  enabled: true
  service:
    type: ClusterIP
    port: 3100
  persistence:
    enabled: true
    storageClassName: "standard"
    size: "20Gi"
  resources:
    requests:
      memory: "500Mi"
      cpu: "500m"
    limits:
      memory: "1Gi"
      cpu: "1"
  retention:
    period: 14d

# Promtail configuration
promtail:
  enabled: true
  service:
    type: ClusterIP
  resources:
    requests:
      memory: "200Mi"
      cpu: "200m"
    limits:
      memory: "400Mi"
      cpu: "500m"
  pipelineStages:
    - json:
        expressions:
          level: level
          message: message
          timestamp: timestamp
    - output:
        source: message

# Alertmanager configuration
alertmanager:
  enabled: true
  service:
    type: ClusterIP
    port: 9093
  persistence:
    enabled: true
    storageClassName: "standard"
    size: "5Gi"
  resources:
    requests:
      memory: "200Mi"
      cpu: "200m"
    limits:
      memory: "400Mi"
      cpu: "500m"
  config:
    global:
      resolve_timeout: 5m
    route:
      receiver: 'email-notifications'
      group_by: ['alertname', 'cluster']
      group_wait: 30s
      group_interval: 5m
      repeat_interval: 1h
      routes:
      - receiver: 'email-notifications'
        match_re:
          severity: 'critical|warning'
    receivers:
    - name: 'email-notifications'
      email_configs:
      - to: '<EMAIL>'
        from: '<EMAIL>'
        smarthost: 'smtp.example.com:587'
        auth_username: 'alertmanager'
        auth_identity: '<EMAIL>'
        auth_password: 'your-email-password'
        send_resolved: true

# Node Exporter configuration
nodeExporter:
  enabled: true
  service:
    type: ClusterIP
  resources:
    requests:
      memory: "100Mi"
      cpu: "100m"
    limits:
      memory: "200Mi"
      cpu: "200m"

# Blackbox Exporter configuration
blackboxExporter:
  enabled: true
  service:
    type: ClusterIP
  resources:
    requests:
      memory: "100Mi"
      cpu: "100m"
    limits:
      memory: "200Mi"
      cpu: "200m"
  config:
    modules:
      http_2xx:
        prober: http
        timeout: 5s
        http:
          method: GET
      http_post_2xx:
        prober: http
        timeout: 5s
        http:
          method: POST
      tcp_connect:
        prober: tcp
        timeout: 5s
      icmp:
        prober: icmp
        timeout: 5s

# Kubernetes Service Monitor configuration
serviceMonitor:
  enabled: true
  selector:
    matchLabels:
      app.kubernetes.io/instance: fastapi
