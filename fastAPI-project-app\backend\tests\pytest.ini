[pytest]
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Add environment variables for tests
env =
    PROJECT_NAME=fastapi-project
    POSTGRES_SERVER=postgres
    POSTGRES_USER=postgres
    FIRST_SUPERUSER=<EMAIL>
    FIRST_SUPERUSER_PASSWORD=FastAPI_Secure_2025!

# Add fixtures directory
pythonpath =
    .
    fixtures

# Add markers
markers =
    integration: mark a test as an integration test
    e2e: mark a test as an end-to-end test
    api: mark a test as an API test

# Add test directories
testpaths =
    api
    unit
    integration
