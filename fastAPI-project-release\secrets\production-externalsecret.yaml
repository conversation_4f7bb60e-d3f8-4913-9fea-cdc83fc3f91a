apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: fastapi-prod-secrets
  namespace: fastapi-helm-prod
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: aws-secrets
    kind: ClusterSecretStore
  target:
    name: fastapi-prod-secrets
    creationPolicy: Owner
  data:
    - secretKey: POSTGRES_USERNAME
      remoteRef:
        key: production/postgres/credentials
        property: username
    - secretKey: POSTGRES_PASSWORD
      remoteRef:
        key: production/postgres/credentials
        property: password
    - secretKey: POSTGRES_DB
      remoteRef:
        key: production/postgres/credentials
        property: database
    - secretKey: APP_SECRET_KEY
      remoteRef:
        key: production/app/secrets
        property: secretKey