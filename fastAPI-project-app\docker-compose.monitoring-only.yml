# Platform-independent monitoring stack
# Compatible with Windows, macOS, and Linux

services:
  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    restart: unless-stopped
    networks:
      - monitoring

  loki:
    image: grafana/loki:latest
    container_name: loki
    command: -config.file=/etc/loki/config.yml
    volumes:
      - ./monitoring/loki-config.yml:/etc/loki/config.yml
      - loki_data:/loki
    ports:
      - "3100:3100"
    restart: unless-stopped
    networks:
      - monitoring

  # Platform-independent Promtail configuration
  # Does not rely on host-specific paths
  promtail:
    image: grafana/promtail:latest
    container_name: promtail
    command: -config.file=/etc/promtail/config.yml
    volumes:
      - ./monitoring/promtail-config-simple.yml:/etc/promtail/config.yml
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/log:/var/log:ro
    restart: unless-stopped
    networks:
      - monitoring
    depends_on:
      - loki

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana-datasources.yml:/etc/grafana/provisioning/datasources/datasources.yml
      - ./monitoring/grafana-dashboards.yml:/etc/grafana/provisioning/dashboards/dashboards.yml
      - ./monitoring/dashboards:/etc/grafana/provisioning/dashboards/
    environment:
      - GF_SECURITY_ADMIN_USER=admin
      - GF_SECURITY_ADMIN_PASSWORD=admin
      - GF_INSTALL_PLUGINS=grafana-piechart-panel,grafana-worldmap-panel
    ports:
      - "3001:3000"  # Changed to 3001 to avoid conflicts with other services
    restart: unless-stopped
    networks:
      - monitoring
    depends_on:
      - prometheus
      - loki

networks:
  monitoring:
    name: monitoring-network

volumes:
  prometheus_data:
  grafana_data:
  loki_data:
