{"name": "fastapi-project-app", "version": "0.1.0", "private": true, "packageManager": "pnpm@10.7.0", "workspaces": ["frontend", "backend"], "scripts": {"commit": "git-cz", "release": "semantic-release", "create-branch": "node scripts/create-branch.js", "backend:install": "pip install -r backend/requirements.txt", "test": "pnpm run -r test", "test:unit": "pnpm run -r test:unit", "test:e2e": "pnpm run -r test:e2e"}, "devDependencies": {"@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@semantic-release/changelog": "^6.0.3", "@semantic-release/commit-analyzer": "^13.0.1", "@semantic-release/git": "^10.0.1", "@semantic-release/release-notes-generator": "^14.0.3", "@testing-library/jest-dom": "^6.6.3", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/testing-library__jest-dom": "^6.0.0", "chalk": "^4.1.2", "commitizen": "^4.3.0", "cz-conventional-changelog": "^3.3.0", "inquirer": "^8.2.6", "semantic-release": "^24.2.3", "vitest": "^1.6.1"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "dependencies": {"@chakra-ui/react": "^3.8.0", "@tanstack/react-router": "1.19.1", "react-hook-form": "7.49.3", "react-icons": "^5.4.0"}}