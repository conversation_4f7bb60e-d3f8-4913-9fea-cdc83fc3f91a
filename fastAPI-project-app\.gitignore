# ----------------------------------------
# ID<PERSON> and Editor Files
# ----------------------------------------
.vscode/
.idea/
.codebuddy/
.DS_Store
*.swp
*.swo
*~

# ----------------------------------------
# Environment and Secrets
# ----------------------------------------
# Environment variables
.env
.env.local
.env.*.local
.secrets

# ----------------------------------------
# Node.js and Frontend
# ----------------------------------------
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
.pnpm-store/

# ----------------------------------------
# Python and Backend
# ----------------------------------------
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
.venv/
venv/
ENV/
.env/
*.egg-info/
.installed.cfg
*.egg
backend/test.db

# ----------------------------------------
# Testing and Coverage
# ----------------------------------------
backend/test-results/
frontend/test-results/
frontend/playwright-report/
frontend/Dockerfile.test
scripts/run-frontend-tests.sh
frontend/jest.config.js
Dockerfile
/blob-report/
/playwright/.cache/
.coverage
htmlcov/
.pytest_cache/

# ----------------------------------------
# Build and Distribution
# ----------------------------------------
dist/
build/
*.log

# ----------------------------------------
# Docker
# ----------------------------------------
.docker-data/

# ----------------------------------------
# Temporary Files
# ----------------------------------------
tmp/
temp/
.tmp/


# ----------------------------------------
# AI Assistant
# ----------------------------------------
.github/copiilot-configuration.md
!.windsurfrules
.windsurfconfig
.aider.*

# Ignore GitHub Copilot instructions symlink
.github/copilot-instructions.md

# Additional test and development directories
.aider.tags.cache.v3/
prometheus/
frontend/coverage/
