# Local Test Environment Variables
# This file contains settings specific to the local test environment
# Copy this file to .env.local-test and fill in the values
#
# NOTE: Common settings like AWS credentials, account ID, and project name
# should be defined in .env.base instead of here.
# See .env.base.example for more information.

# AWS Region Configuration
AWS_DEFAULT_REGION=eu-west-2

# Environment setting
ENVIRONMENT=local-test

# For local testing, you can use dummy AWS credentials
# These will override the credentials in .env.base when running locally
AWS_ACCESS_KEY_ID=dummy-key-for-local-testing
AWS_SECRET_ACCESS_KEY=dummy-secret-for-local-testing
AWS_ACCOUNT_ID=************

# GitHub Actions Secrets (Required for local testing with act)
# These are used by the GitHub Actions workflow when testing locally
GITHUB_TOKEN=your-github-token
