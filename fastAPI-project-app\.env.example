# Application
ENVIRONMENT=local
DEBUG=true

# Database
POSTGRES_SERVER=db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=app

# Security
SECRET_KEY=your-secret-key-here
CORS_ORIGINS=http://localhost:3000

# Admin User
FIRST_SUPERUSER=<EMAIL>
FIRST_SUPERUSER_PASSWORD=adminadmin123

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=DevOps Demo
BACKEND_CORS_ORIGINS=["http://localhost:3000"]

# Frontend
NEXT_PUBLIC_API_URL=http://localhost:8000
