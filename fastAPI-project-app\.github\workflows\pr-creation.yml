name: Reusable PR Creation

on:
  workflow_call:
    inputs:
      source_branch:
        required: true
        type: string
      target_branch:
        required: true
        type: string
      pr_title:
        required: true
        type: string
      pr_body:
        required: true
        type: string
    secrets:
      MACHINE_USER_TOKEN:
        required: true

permissions:
  contents: write
  pull-requests: write

jobs:
  create-pr:
    runs-on: ubuntu-latest
    steps:
      - name: Install GitHub CLI
        run: |
          sudo apt-get update
          sudo apt-get install -y gh

      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Create or Update Pull Request
        env:
          GH_TOKEN: ${{ secrets.MACHINE_USER_TOKEN }}
        run: |
          echo "Source branch: ${{ inputs.source_branch }}"
          echo "Target branch: ${{ inputs.target_branch }}"

          existing_pr_url=$(gh pr list --head "${{ inputs.source_branch }}" --base "${{ inputs.target_branch }}" --state open --json url --jq '.[0].url')

          if [ -n "$existing_pr_url" ]; then
            echo "Pull request already exists: $existing_pr_url"
            pr_url="$existing_pr_url"
            pr_number=$(basename "$pr_url" | cut -d'/' -f1)
            echo "pull-request-url=$pr_url" >> $GITHUB_OUTPUT
            echo "pull-request-number=$pr_number" >> $GITHUB_OUTPUT
            exit 0
          fi

          pr_url=$(gh pr create \
            --base "${{ inputs.target_branch }}" \
            --head "${{ inputs.source_branch }}" \
            --title "${{ inputs.pr_title }}" \
            --body "${{ inputs.pr_body }}")

          echo "PR URL: $pr_url"

          pr_number=$(basename "$pr_url" | cut -d'/' -f1)

          echo "pull-request-url=$pr_url" >> $GITHUB_OUTPUT
          echo "pull-request-number=$pr_number" >> $GITHUB_OUTPUT

      - name: PR Details
        if: success()
        run: |
          echo "PR created or already exists."
