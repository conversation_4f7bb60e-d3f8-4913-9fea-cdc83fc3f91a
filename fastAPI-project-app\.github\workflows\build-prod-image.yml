name: Build and Push Production Images

on:
  push:
    branches:
      - main

permissions:
  contents: write
  packages: write

jobs:
  build-prod-images:
    uses: ./.github/workflows/build-image.yml
    with:
      environment: prod
      version_bump_type: patch

  trigger-release:
    needs: build-prod-images
    if: success()
    uses: ./.github/workflows/trigger-helm-release.yml
    with:
      version: ${{ needs.build-prod-images.outputs.new_version }}
      environment: prod
      backend_image: ghcr.io/${{ github.repository_owner }}/${{ github.event.repository.name }}-backend:${{ needs.build-prod-images.outputs.new_version }}
      frontend_image: ghcr.io/${{ github.repository_owner }}/${{ github.event.repository.name }}-frontend:${{ needs.build-prod-images.outputs.new_version }}
    secrets:
      MACHINE_USER_TOKEN: ${{ secrets.MACHINE_USER_TOKEN }}
