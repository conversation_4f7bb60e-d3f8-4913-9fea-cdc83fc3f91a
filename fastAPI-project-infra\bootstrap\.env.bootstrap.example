# Bootstrap Environment Variables
# This file contains settings specific to the bootstrap environment
# Copy this file to bootstrap/.env.bootstrap and fill in the values
#
# NOTE: Common settings like AWS credentials, account ID, and project name
# should be defined in the root .env.base file, not here.
# See .env.base.example for more information.

# Environment setting
ENVIRONMENT=bootstrap

# AWS Region override for state bucket
# This is needed because some resources like S3 state buckets are created in us-east-1
AWS_DEFAULT_REGION=us-east-1
