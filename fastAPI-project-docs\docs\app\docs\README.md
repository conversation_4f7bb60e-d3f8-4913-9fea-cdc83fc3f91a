# DevOps Demo Application Documentation

Welcome to the documentation for the DevOps Demo Application. This directory contains comprehensive documentation for all aspects of the project.

## Documentation Structure

- **[Development](./development/)**

  - [Development Guide](./development/guide.md) - How to set up and run the application for local development
  - [Development Scripts](./development/scripts.md) - Utility scripts for development, testing, and deployment

- **[Deployment](./deployment/)**

  - [Deployment Guide](./deployment/guide.md) - How to deploy the application to AWS ECS using GitHub Actions

- **[Workflows](./workflows/)**

  - _This directory will contain documentation for GitHub Actions workflows_

- **[Git Hooks](./git-hooks.md)** - Documentation for the pre-commit git hooks setup

- **[Release Notes](./release-notes.md)** - Comprehensive changelog of all project changes

## Component-Specific Documentation

- **[Backend README](../backend/README.md)** - Documentation specific to the backend application
- **[Frontend README](../frontend/README.md)** - Documentation specific to the frontend application

## Contributing to Documentation

When adding new documentation:

1. Place it in the appropriate subdirectory based on its category
2. Update this index file to include a link to the new documentation
3. Follow the established Markdown formatting conventions
4. Include clear headings, code examples, and explanations

For substantial documentation changes, please create a feature branch and submit a pull request.
