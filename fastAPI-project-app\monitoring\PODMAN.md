# Running Monitoring and Logging with <PERSON><PERSON>

This guide explains how to run the monitoring and logging stack using <PERSON><PERSON> instead of Docker Desktop.

## Prerequisites

1. Install Podman:
   - Windows: Install from [Podman Desktop](https://podman-desktop.io/) or via Chocolatey: `choco install podman`
   - macOS: `brew install podman`
   - Linux: Follow the [installation guide](https://podman.io/getting-started/installation) for your distribution

2. Install podman-compose:
   ```bash
   pip install podman-compose
   ```

3. Initialize Podman machine (Windows/macOS only):
   ```bash
   podman machine init
   podman machine start
   ```

## Starting the Monitoring Stack

### Using the Scripts

We provide platform-specific scripts to start the monitoring stack with Podman:

**Windows (PowerShell):**
```powershell
.\scripts\monitoring\start-monitoring-podman.ps1
```

**macOS/Linux (Bash):**
```bash
./scripts/monitoring/start-monitoring-podman.sh
```

### Manual Start

If you prefer to start the stack manually:

```bash
# Create log directories
mkdir -p logs/backend logs/application

# Create Podman network
podman network create monitoring-network

# Start the monitoring stack
podman-compose -f docker-compose.monitoring-only.yml up -d
```

## Accessing the Dashboards

- **Grafana**: http://localhost:3001
  - Username: `admin`
  - Password: `admin`
- **Prometheus**: http://localhost:9090

## Stopping the Monitoring Stack

To stop the monitoring stack:

```bash
podman-compose -f docker-compose.monitoring-only.yml down
```

## Troubleshooting

### Volume Mounts

If you encounter issues with volume mounts, you may need to adjust the SELinux context or use the `:Z` or `:z` suffix for volume mounts in the docker-compose file.

### Network Connectivity

If containers cannot communicate with each other, ensure they are on the same Podman network:

```bash
podman network inspect monitoring-network
```

### Podman Socket

Some tools might expect the Docker socket at `/var/run/docker.sock`. For Podman, you may need to create a symbolic link or use the Podman socket directly.

## Release Repository Integration

To start the release monitoring that integrates with this stack:

**Windows (PowerShell):**
```powershell
cd ../fastAPI-project-release
.\scripts\start-monitoring-podman.ps1
```

**macOS/Linux (Bash):**
```bash
cd ../fastAPI-project-release
./scripts/start-monitoring-podman.sh
```
