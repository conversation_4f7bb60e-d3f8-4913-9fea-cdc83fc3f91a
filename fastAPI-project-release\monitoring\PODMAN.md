# Running Release Monitoring with <PERSON><PERSON>

This guide explains how to run the release monitoring stack using <PERSON><PERSON> instead of Docker Desktop.

## Prerequisites

1. Install Podman:
   - Windows: Install from [Podman Desktop](https://podman-desktop.io/) or via Chocolatey: `choco install podman`
   - macOS: `brew install podman`
   - Linux: Follow the [installation guide](https://podman.io/getting-started/installation) for your distribution

2. Install podman-compose:
   ```bash
   pip install podman-compose
   ```

3. Initialize Podman machine (Windows/macOS only):
   ```bash
   podman machine init
   podman machine start
   ```

## Starting the Release Monitoring Stack

### Using the Scripts

We provide platform-specific scripts to start the release monitoring stack with Podman:

**Windows (PowerShell):**
```powershell
.\scripts\start-monitoring-podman.ps1
```

**macOS/Linux (Bash):**
```bash
./scripts/start-monitoring-podman.sh
```

These scripts will:
1. Check if the app monitoring stack is running
2. Start the app monitoring stack if needed
3. Start the release monitoring stack

### Manual Start

If you prefer to start the stack manually:

```bash
# Ensure the app monitoring stack is running
cd ../fastAPI-project-app
podman-compose -f docker-compose.monitoring-only.yml up -d

# Start the release monitoring
cd ../fastAPI-project-release
podman-compose -f docker-compose.monitoring.yml up -d
```

## Accessing the Dashboards

- **Grafana**: http://localhost:3001
  - Username: `admin`
  - Password: `admin`

## Stopping the Monitoring Stack

To stop the release monitoring stack:

```bash
podman-compose -f docker-compose.monitoring.yml down
```

## Troubleshooting

### Network Connectivity

The release monitoring stack depends on the app monitoring stack's network. If you encounter connectivity issues:

1. Ensure the app monitoring stack is running
2. Check that the monitoring network exists:
   ```bash
   podman network inspect monitoring-network
   ```
3. Ensure the release monitoring container is connected to the network:
   ```bash
   podman inspect release-monitoring | grep -A 10 "Networks"
   ```

### Container Logs

If the release monitoring container is not working as expected, check its logs:

```bash
podman logs release-monitoring
```

### Restarting the Stack

If you need to restart the entire monitoring stack:

```bash
# Stop and remove all containers
podman-compose -f docker-compose.monitoring.yml down

# Go to the app repository
cd ../fastAPI-project-app
podman-compose -f docker-compose.monitoring-only.yml down

# Start the app monitoring stack
podman-compose -f docker-compose.monitoring-only.yml up -d

# Go back to the release repository
cd ../fastAPI-project-release
podman-compose -f docker-compose.monitoring.yml up -d
```
