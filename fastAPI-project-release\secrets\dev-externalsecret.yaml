apiVersion: external-secrets.io/v1beta1
kind: ExternalSecret
metadata:
  name: fastapi-dev-secrets
  namespace: fastapi-helm-dev
spec:
  refreshInterval: 1h
  secretStoreRef:
    name: aws-secrets
    kind: ClusterSecretStore
  target:
    name: fastapi-dev-secrets
    creationPolicy: Owner
  data:
    - secretKey: POSTGRES_USERNAME
      remoteRef:
        key: dev/postgres/credentials
        property: username
    - secretKey: POSTGRES_PASSWORD
      remoteRef:
        key: dev/postgres/credentials
        property: password
    - secretKey: POSTGRES_DB
      remoteRef:
        key: dev/postgres/credentials
        property: database
    - secretKey: APP_SECRET_KEY
      remoteRef:
        key: dev/app/secrets
        property: secretKey