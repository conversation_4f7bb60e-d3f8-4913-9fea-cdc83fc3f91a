# Logging

This directory contains the logging configuration for the FastAPI application.

## Overview

The logging setup uses <PERSON> and <PERSON><PERSON><PERSON> to collect and store logs from the application, which can then be visualized in Grafana.

## Components

- **Loki**: Collects and stores logs
- **Promtail**: Forwards logs to Loki
- **<PERSON>ana**: Visualizes logs

## Configuration

The logging configuration is defined in the following files:

- `loki-config.yml`: Loki configuration
- `promtail-config.yml`: Promtail configuration

## Setup

The logging setup is integrated with the monitoring setup. To set up logging:

1. Start the application with monitoring and logging enabled:
   ```bash
   make up-monitoring
   ```

2. Access the Grafana dashboard:
   - URL: http://grafana.localhost
   - Default credentials: admin/admin

## Log Levels

The application uses the following log levels:

- **DEBUG**: Detailed information, typically of interest only when diagnosing problems
- **INFO**: Confirmation that things are working as expected
- **WARNING**: An indication that something unexpected happened, or may happen in the near future
- **ERROR**: Due to a more serious problem, the software has not been able to perform some function
- **CRITICAL**: A serious error, indicating that the program itself may be unable to continue running

## Log Format

Logs are formatted as JSON with the following fields:

- **timestamp**: The time the log was generated
- **level**: The log level
- **message**: The log message
- **module**: The module that generated the log
- **function**: The function that generated the log
- **line**: The line number that generated the log
- **request_id**: The ID of the request that generated the log (if applicable)
- **user_id**: The ID of the user that generated the log (if applicable)

## Viewing Logs

Logs can be viewed in Grafana:

1. Go to the Explore view
2. Select Loki as the data source
3. Use LogQL to query logs

Example queries:

- `{job="fastapi"}`: All logs from the FastAPI application
- `{job="fastapi"} |= "ERROR"`: All ERROR logs from the FastAPI application
- `{job="fastapi"} |= "user_id=123"`: All logs for user with ID 123
