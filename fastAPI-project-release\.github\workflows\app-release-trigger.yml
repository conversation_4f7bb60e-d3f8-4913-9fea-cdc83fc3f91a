name: App Release Trigger

on:
  repository_dispatch:
    types: [app-release]

permissions:
  contents: write
  packages: read

jobs:
  create-release:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Set up He<PERSON>
        uses: azure/setup-helm@v3.5
        with:
          version: 'v3.12.0'

      - name: Install yq
        run: |
          sudo wget -qO /usr/local/bin/yq https://github.com/mikefarah/yq/releases/download/v4.44.1/yq_linux_amd64
          sudo chmod +x /usr/local/bin/yq

      - name: Parse payload and validate version
        id: parse
        run: |
          # Get the raw version from the payload
          RAW_VERSION="${{ github.event.client_payload.version }}"
          echo "raw_version=$RAW_VERSION" >> $GITHUB_OUTPUT

          # Check if semantic_version is provided in the payload
          if [ -n "${{ github.event.client_payload.semantic_version }}" ]; then
            echo "Semantic version provided in payload: ${{ github.event.client_payload.semantic_version }}"
            SEMANTIC_VERSION="${{ github.event.client_payload.semantic_version }}"

            # For staging, append -stg to the semantic version
            if [[ "${{ github.event.client_payload.environment }}" == "stg" ]]; then
              VALID_VERSION="${SEMANTIC_VERSION}-stg"
              echo "Using semantic version with staging suffix: $VALID_VERSION"
            else
              VALID_VERSION="$SEMANTIC_VERSION"
              echo "Using semantic version as is: $VALID_VERSION"
            fi
          else
            # No semantic version provided, fall back to the raw version
            echo "No semantic version provided in payload, using raw version"

            # Check if the version is a semantic version (matches pattern like 1.2.3 or 1.2.3-stg)
            if [[ "$RAW_VERSION" =~ ^[0-9]+\.[0-9]+\.[0-9]+(-[a-zA-Z0-9]+)?$ ]]; then
              echo "Version $RAW_VERSION is a valid semantic version"
              VALID_VERSION="$RAW_VERSION"
            else
              # If it's not a semantic version (e.g., a git hash or other format)
              echo "Version $RAW_VERSION is not a valid semantic version"

              # Check if it's a staging version (contains 'stg' or starts with a git hash)
              if [[ "$RAW_VERSION" == *"stg"* ]] || [[ "${{ github.event.client_payload.environment }}" == "stg" ]]; then
                # For staging, use 0.0.1 as the base version with the raw version as a prerelease identifier
                # Replace any characters that aren't allowed in semver with dashes
                SAFE_VERSION=$(echo "$RAW_VERSION" | sed 's/[^0-9a-zA-Z-]/-/g')
                VALID_VERSION="0.0.1-$SAFE_VERSION"
                echo "Converting to valid semantic version for staging: $VALID_VERSION"
              else
                # For production, we should have a valid semver, so this is an error
                echo "::error::Production releases must use semantic versioning (e.g., 1.2.3)"
                exit 1
              fi
            fi
          fi

          echo "version=$VALID_VERSION" >> $GITHUB_OUTPUT
          echo "backend_image=${{ github.event.client_payload.backend_image }}" >> $GITHUB_OUTPUT
          echo "frontend_image=${{ github.event.client_payload.frontend_image }}" >> $GITHUB_OUTPUT
          echo "environment=${{ github.event.client_payload.environment }}" >> $GITHUB_OUTPUT

      - name: Package Helm charts
        run: |
          # Create dist directories
          mkdir -p dist/fastapi-dev dist/fastapi-staging dist/fastapi-prod

          # First, update the values files with the correct image tags
          # For dev environment
          yq e '.backend.tag = "${{ steps.parse.outputs.version }}-stg"' -i config/helm/dev.yaml
          yq e '.frontend.tag = "${{ steps.parse.outputs.version }}-stg"' -i config/helm/dev.yaml

          # For staging environment
          yq e '.backend.tag = "${{ steps.parse.outputs.version }}-stg"' -i config/helm/staging.yaml
          yq e '.frontend.tag = "${{ steps.parse.outputs.version }}-stg"' -i config/helm/staging.yaml

          # For production environment
          yq e '.backend.tag = "${{ steps.parse.outputs.version }}"' -i config/helm/production.yaml
          yq e '.frontend.tag = "${{ steps.parse.outputs.version }}"' -i config/helm/production.yaml

          # Package dev chart
          helm package charts/fastapi \
            --version ${{ steps.parse.outputs.version }} \
            --app-version "${{ steps.parse.outputs.version }}-stg" \
            -d dist/fastapi-dev

          # Package staging chart
          helm package charts/fastapi \
            --version ${{ steps.parse.outputs.version }} \
            --app-version "${{ steps.parse.outputs.version }}-stg" \
            -d dist/fastapi-staging

          # Package prod chart
          helm package charts/fastapi \
            --version ${{ steps.parse.outputs.version }} \
            --app-version "${{ steps.parse.outputs.version }}" \
            -d dist/fastapi-prod

      - name: Check if charts were packaged
        id: check_charts
        run: |
          # Check if any charts were packaged
          DEV_CHARTS=$(find dist/fastapi-dev -name "*.tgz" 2>/dev/null | wc -l)
          STAGING_CHARTS=$(find dist/fastapi-staging -name "*.tgz" 2>/dev/null | wc -l)
          PROD_CHARTS=$(find dist/fastapi-prod -name "*.tgz" 2>/dev/null | wc -l)

          echo "Found $DEV_CHARTS charts in dev, $STAGING_CHARTS in staging, $PROD_CHARTS in prod"

          TOTAL_CHARTS=$((DEV_CHARTS + STAGING_CHARTS + PROD_CHARTS))
          if [ "$TOTAL_CHARTS" -eq 0 ]; then
            echo "No charts were packaged. This might indicate an issue with the Helm package commands."
            echo "charts_exist=false" >> $GITHUB_OUTPUT
          else
            echo "Found $TOTAL_CHARTS charts in total."
            echo "charts_exist=true" >> $GITHUB_OUTPUT
          fi

      - name: Create GitHub Release
        if: steps.check_charts.outputs.charts_exist == 'true' && steps.check_release.outputs.release_exists == 'false'
        uses: softprops/action-gh-release@v2.0.2
        env:
          GITHUB_TOKEN: ${{ secrets.MACHINE_USER_TOKEN }}
        with:
          tag_name: v${{ steps.parse.outputs.raw_version }}
          name: Release v${{ steps.parse.outputs.raw_version }}
          files: |
            dist/fastapi-dev/*.tgz
            dist/fastapi-staging/*.tgz
            dist/fastapi-prod/*.tgz
          generate_release_notes: true
          draft: false
          prerelease: ${{ github.event.client_payload.environment == 'stg' }}
          body: |
            ## Environment-specific Helm Charts

            This release includes Helm charts for the following environments:
            - Development (using staging images)
            - Staging
            - Production

            ### Version Information
            - Raw Version: v${{ steps.parse.outputs.raw_version }}
            - Semantic Version for Helm: ${{ steps.parse.outputs.version }}
            - Environment: ${{ steps.parse.outputs.environment }}
            - Original Semantic Version: ${{ github.event.client_payload.semantic_version }}

            ### Image Tags
            - Dev/Staging: ${{ steps.parse.outputs.version }}-stg
            - Production: ${{ steps.parse.outputs.version }}

      - name: Trigger Helm Update
        if: steps.check_charts.outputs.charts_exist == 'true' && steps.check_release.outputs.release_exists == 'false'
        uses: peter-evans/repository-dispatch@v3
        with:
          token: ${{ secrets.MACHINE_USER_TOKEN }}
          repository: ${{ github.repository }}
          event-type: helm-update-trigger
          client-payload: |
            {
              "tag": "${{ steps.parse.outputs.version }}",
              "env": "${{ steps.parse.outputs.environment }}"
            }

      - name: Debug Release Creation
        if: steps.check_charts.outputs.charts_exist == 'true' && steps.check_release.outputs.release_exists == 'false'
        run: |
          echo "A new GitHub release was created with tag v${{ steps.parse.outputs.raw_version }}"
          echo "Using semantic version ${{ steps.parse.outputs.version }} for Helm charts"
          echo "This should trigger the update-helm.yaml workflow via the release: [published] event"
          echo "If update-helm.yaml doesn't run, check if the release was actually published (not a draft)"
          echo "Staging releases are marked as pre-releases: ${{ github.event.client_payload.environment == 'stg' }}"
