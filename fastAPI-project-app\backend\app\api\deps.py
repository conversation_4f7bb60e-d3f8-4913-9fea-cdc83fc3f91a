from typing import Annotated

from fastapi import Depends, HTTPException, status
from fastapi.security import O<PERSON><PERSON>2<PERSON><PERSON>wordBearer
from jose import jwt
from pydantic import ValidationError
from sqlmodel import Session

from app import crud
from app.core.config import settings
from app.core.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.db.session import get_session
from app.models import TokenPayload, User

oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl=f"{settings.API_V1_STR}/login/access-token"
)

# Use the session factory function directly
get_db = get_session

# Type aliases for better readability
SessionDep = Annotated[Session, Depends(get_db)]


async def get_current_user(db: SessionDep, token: str = Depends(oauth2_scheme)) -> User:
    try:
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[ALGORITHM])
        token_data = TokenPayload(**payload)
    except (jwt.JWTError, ValidationError):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Could not validate credentials",
        )
    user = crud.get_user_by_email(db, token_data.sub)
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user


# Type alias for current user after function is defined
CurrentUser = Annotated[User, Depends(get_current_user)]


async def get_current_active_user(
    current_user: User = Depends(get_current_user),
) -> User:
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user


async def get_current_active_superuser(current_user: CurrentUser) -> User:
    if not current_user.is_superuser:
        raise HTTPException(
            status_code=403, detail="The user doesn't have enough privileges"
        )
    return current_user
