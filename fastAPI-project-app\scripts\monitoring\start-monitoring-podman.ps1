# PowerShell script to start the monitoring stack with <PERSON><PERSON>
# Works on Windows with <PERSON><PERSON>

# Navigate to the project root directory
$projectRoot = Split-Path -Parent (Split-Path -Parent $PSScriptRoot)
Set-Location $projectRoot

Write-Host "Starting monitoring stack with <PERSON><PERSON> from $projectRoot..."

# Check if <PERSON><PERSON> is running
try {
    podman info | Out-Null
    Write-Host "<PERSON><PERSON> is running."
} catch {
    Write-Host "Error: <PERSON><PERSON> is not running. Please start <PERSON><PERSON> and try again." -ForegroundColor Red
    exit 1
}

# Create log directories if they don't exist
$logDirs = @("logs/backend", "logs/application")
foreach ($dir in $logDirs) {
    if (-not (Test-Path $dir)) {
        New-Item -Path $dir -ItemType Directory -Force | Out-Null
        Write-Host "Created log directory: $dir"
    }
}

# Create Podman network if it doesn't exist
$networkExists = podman network ls --filter "name=monitoring-network" --format "{{.Name}}"
if (-not $networkExists) {
    Write-Host "Creating Podman network: monitoring-network"
    podman network create monitoring-network
}

# Start the monitoring stack
Write-Host "Starting monitoring services with <PERSON><PERSON>..."
podman-compose -f docker-compose.monitoring-only.yml up -d

# Check if services are running
Write-Host "Checking if services are running..."
$services = @("prometheus", "loki", "grafana")
foreach ($service in $services) {
    $status = podman ps --filter "name=$service" --format "{{.Status}}"
    if ($status) {
        Write-Host "$service is running: $status" -ForegroundColor Green
    } else {
        Write-Host "$service is not running" -ForegroundColor Yellow
    }
}

Write-Host "Monitoring setup complete."
Write-Host "You can access the dashboards at:"
Write-Host "- Grafana: http://localhost:3001"
Write-Host "- Prometheus: http://localhost:9090"
