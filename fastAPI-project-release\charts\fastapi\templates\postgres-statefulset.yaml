apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ .Values.database.name }}
  namespace: {{ .Values.app.namespace }}
  labels:
    app: {{ .Values.database.name }}
spec:
  serviceName: {{ .Values.database.service.name }}
  replicas: 1
  selector:
    matchLabels:
      app: {{ .Values.database.name }}
  template:
    metadata:
      labels:
        app: {{ .Values.database.name }}
    spec:
      containers:
        - name: {{ .Values.database.name }}
          image: "{{ .Values.database.image }}:{{ .Values.database.tag }}"
          imagePullPolicy: IfNotPresent
          ports:
            - name: postgres
              containerPort: 5432
              protocol: TCP
          env:
            - name: POSTGRES_USER
              value: {{ .Values.database.credentials.username }}
            - name: POSTGRES_PASSWORD
              value: {{ .Values.database.credentials.password }}
            - name: POSTGRES_DB
              value: {{ .Values.database.credentials.database }}
          volumeMounts:
            - name: postgres-data
              mountPath: /var/lib/postgresql/data
          resources:
            requests:
              memory: "256Mi"
              cpu: "250m"
            limits:
              memory: "512Mi"
              cpu: "500m"
          livenessProbe:
            exec:
              command:
                - pg_isready
                - -U
                - {{ .Values.database.credentials.username }}
            initialDelaySeconds: 30
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
          readinessProbe:
            exec:
              command:
                - pg_isready
                - -U
                - {{ .Values.database.credentials.username }}
            initialDelaySeconds: 5
            periodSeconds: 10
            timeoutSeconds: 5
            failureThreshold: 3
      {{- with .Values.securityContext }}
      securityContext:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      volumes:
        - name: postgres-data
          persistentVolumeClaim:
            claimName: postgres-pvc
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: {{ .Values.app.namespace }}
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: {{ .Values.database.storage.size }}