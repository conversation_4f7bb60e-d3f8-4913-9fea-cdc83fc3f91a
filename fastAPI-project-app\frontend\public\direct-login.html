<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Direct Login</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 8px;
            box-sizing: border-box;
        }
        button {
            padding: 10px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            cursor: pointer;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Direct FastAPI Login</h1>

    <div class="form-group">
        <label for="username">Email:</label>
        <input type="email" id="username" value="<EMAIL>">
    </div>

    <div class="form-group">
        <label for="password">Password:</label>
        <input type="password" id="password" value="FastAPI_Secure_2025!">
    </div>

    <button id="loginButton">Login</button>

    <h3>Response:</h3>
    <pre id="response">Click the login button to authenticate...</pre>

    <script>
        document.getElementById('loginButton').addEventListener('click', async () => {
            const responseElement = document.getElementById('response');
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            responseElement.textContent = 'Attempting login...';

            try {
                // Create form data
                const formData = new URLSearchParams();
                formData.append('username', username);
                formData.append('password', password);
                formData.append('grant_type', 'password');

                console.log('Form data:', formData.toString());

                // Make the direct request to the backend
                const response = await fetch('http://api.localhost/api/v1/login/access-token', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: formData,
                });

                // Get response details
                const status = response.status;
                const statusText = response.statusText;

                if (response.ok) {
                    // Try to parse the response as JSON
                    const data = await response.json();

                    // Store the token in localStorage
                    if (data.access_token) {
                        localStorage.setItem('access_token', data.access_token);

                        responseElement.textContent = JSON.stringify({
                            status,
                            statusText,
                            message: 'Login successful! Token stored in localStorage.',
                            token: data.access_token
                        }, null, 2);

                        // Redirect to the dashboard after 2 seconds
                        setTimeout(() => {
                            window.location.href = '/';
                        }, 2000);
                    } else {
                        responseElement.textContent = JSON.stringify({
                            status,
                            statusText,
                            message: 'Login successful but no token received.',
                            data
                        }, null, 2);
                    }
                } else {
                    // Handle error response
                    let errorData;
                    try {
                        errorData = await response.json();
                    } catch (e) {
                        errorData = await response.text();
                    }

                    responseElement.textContent = JSON.stringify({
                        status,
                        statusText,
                        message: 'Login failed.',
                        error: errorData
                    }, null, 2);
                }
            } catch (error) {
                responseElement.textContent = 'Error: ' + error.message;
                console.error('Login error:', error);
            }
        });
    </script>
</body>
</html>
