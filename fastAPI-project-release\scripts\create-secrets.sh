#!/bin/bash

# Generate secure passwords
GRAFANA_ADMIN_PASSWORD=$(openssl rand -base64 32)
ALERTMANAGER_EMAIL_PASSWORD=$(openssl rand -base64 32)

# Create Grafana secrets
kubectl create secret generic grafana-secrets \
  --from-literal=admin-user=admin \
  --from-literal=admin-password="$GRAFANA_ADMIN_PASSWORD" \
  -n monitoring

# Create Alertmanager secrets
kubectl create secret generic alertmanager-secrets \
  --from-literal=smtp_password="$ALERTMANAGER_EMAIL_PASSWORD" \
  -n monitoring

# Print the generated passwords
echo "Generated passwords:"
echo "Grafana Admin Password: $GRAFANA_ADMIN_PASSWORD"
echo "Alertmanager Email Password: $ALERTMANAGER_EMAIL_PASSWORD"

# Save passwords to a secure file
echo "Grafana Admin Password: $GRAFANA_ADMIN_PASSWORD" > secrets.txt
echo "Alertmanager Email Password: $ALERTMANAGER_EMAIL_PASSWORD" >> secrets.txt
chmod 600 secrets.txt
