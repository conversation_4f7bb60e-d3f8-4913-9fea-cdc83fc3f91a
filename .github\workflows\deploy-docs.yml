name: Deploy Documentation

on:
  push:
    branches:
      - main
  workflow_dispatch:

permissions:
  contents: write

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: Create missing documentation directories
        run: |
          # Create directories for documentation files
          mkdir -p docs/app/docs/deployment
          mkdir -p docs/app/docs/workflow
          mkdir -p docs/app/frontend
          mkdir -p docs/app/backend
          mkdir -p docs/release/docs
          mkdir -p docs/infra/examples/git-workflow

          # Create placeholder files for missing documentation
          touch docs/app/docs/deployment/guide.md
          touch docs/app/docs/git-hooks.md
          touch docs/app/docs/workflow/github-actions.md
          touch docs/app/frontend/README.md
          touch docs/app/backend/README.md
          touch docs/release/docs/release-strategy.md
          touch docs/release/docs/troubleshooting.md
          touch docs/release/docs/quick-start.md
          touch docs/infra/examples/git-workflow/example.sh

      - name: Build documentation
        run: mkdocs build

      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./site
          publish_branch: gh-pages
          force_orphan: true

      - name: Create GitHub Release
        if: github.event_name == 'push' && github.ref == 'refs/heads/main'
        env:
          GH_TOKEN: ${{ secrets.MACHINE_USER_TOKEN }}
        run: |
          # Get the current date for the release tag
          RELEASE_DATE=$(date +"%Y.%m.%d")

          # Create a release tag
          git tag -a "v$RELEASE_DATE" -m "Documentation release $RELEASE_DATE"
          git push origin "v$RELEASE_DATE"

          # Create a GitHub release
          gh release create "v$RELEASE_DATE" \
            --title "Documentation Release $RELEASE_DATE" \
            --notes "Documentation update released on $RELEASE_DATE" \
            --target main
