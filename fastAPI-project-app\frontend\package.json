{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host 0.0.0.0", "build": "tsc -p tsconfig.build.json && vite build", "lint": "biome check --apply-unsafe --no-errors-on-unmatched --files-ignore-unknown=true ./", "format:check": "biome check --no-errors-on-unmatched --files-ignore-unknown=true ./", "format": "biome check --apply-unsafe src/ tests/", "biome": "biome", "preview": "vite preview", "generate-client": "openapi-ts", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:unit": "vitest run", "clean": "rm -rf node_modules/.cache dist"}, "dependencies": {"@chakra-ui/react": "^3.15.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^6.4.8", "@tanstack/react-query": "^5.28.14", "@tanstack/react-query-devtools": "^5.28.14", "@tanstack/react-router": "1.19.1", "axios": "^1.8.2", "form-data": "4.0.0", "next-themes": "^0.4.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.13", "react-hook-form": "7.49.3", "react-icons": "^5.5.0", "zod": "^3.22.4"}, "devDependencies": {"@biomejs/biome": "1.6.1", "@chakra-ui/cli": "^3.14.2", "@hey-api/openapi-ts": "^0.57.0", "@playwright/test": "^1.45.2", "@tanstack/router-devtools": "1.19.1", "@tanstack/router-vite-plugin": "1.19.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^14.5.2", "@types/node": "^20.17.28", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/testing-library__jest-dom": "^6.0.0", "@vitejs/plugin-react-swc": "^3.5.0", "@vitest/coverage-v8": "^1.4.0", "dotenv": "^16.4.5", "jsdom": "^24.1.3", "msw": "^2.7.3", "prettier": "^3.5.3", "typescript": "^5.2.2", "vite": "^6.2.1", "vitest": "^1.6.1"}}