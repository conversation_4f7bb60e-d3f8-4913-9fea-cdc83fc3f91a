{"branches": ["main"], "plugins": [["@semantic-release/commit-analyzer", {"preset": "angular", "releaseRules": [{"message": "*Merge pull request*from*feat/*", "release": "minor"}, {"message": "*Merge pull request*from*fix/*", "release": "patch"}, {"message": "*Automated PR from feature branch*", "release": "minor"}, {"message": "*Automated PR from fix branch*", "release": "patch"}]}], "@semantic-release/release-notes-generator", "@semantic-release/changelog", "@semantic-release/npm", ["@semantic-release/git", {"assets": ["package.json", "CHANGELOG.md"], "message": "chore(release): ${nextRelease.version} [skip ci]\n\n${nextRelease.notes}"}], "@semantic-release/github"]}